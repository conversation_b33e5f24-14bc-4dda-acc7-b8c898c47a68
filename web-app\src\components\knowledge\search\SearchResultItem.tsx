'use client';

// MySQLAi.de - 搜索结果项组件
// 提供搜索结果高亮显示、相关性分数、快速操作功能

import React, { useState } from 'react';
import Link from 'next/link';
import {
  FileText,
  Tag,
  Calendar,
  Star,
  Eye,
  Bookmark,
  Share,
  ChevronRight
} from 'lucide-react';

// 搜索结果项类型
interface SearchResultItemData {
  id: string;
  title: string;
  description?: string;
  content?: string;
  category?: {
    id: string;
    name: string;
    icon?: string;
    color?: string;
  };
  tags?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  last_updated: string;
  created_at: string;
  view_count?: number;
  relevanceScore?: number;
  highlight?: {
    title?: string;
    description?: string;
    content?: string;
  };
}

// 组件属性
interface SearchResultItemProps {
  item: SearchResultItemData;
  query?: string;
  onBookmark?: (id: string, bookmarked: boolean) => void;
  onShare?: (item: SearchResultItemData) => void;
  showRelevanceScore?: boolean;
  showActions?: boolean;
  className?: string;
  isBookmarked?: boolean;
}

// 难度配置
const DIFFICULTY_CONFIG = {
  beginner: { label: '初级', color: 'bg-green-100 text-green-800', icon: '●' },
  intermediate: { label: '中级', color: 'bg-yellow-100 text-yellow-800', icon: '●●' },
  advanced: { label: '高级', color: 'bg-orange-100 text-orange-800', icon: '●●●' },
  expert: { label: '专家', color: 'bg-red-100 text-red-800', icon: '●●●●' }
};

export default function SearchResultItem({
  item,
  query,
  onBookmark,
  onShare,
  showRelevanceScore = false,
  showActions = true,
  className = '',
  isBookmarked = false
}: SearchResultItemProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 处理高亮文本渲染
  const renderHighlightedText = (text: string, highlightedText?: string) => {
    if (!highlightedText || !query) {
      return <span>{text}</span>;
    }

    // 如果有高亮文本，直接使用（已包含<mark>标签）
    return (
      <span 
        dangerouslySetInnerHTML={{ __html: highlightedText }}
        className="[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded"
      />
    );
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    return `${Math.floor(diffDays / 30)}月前`;
  };

  // 处理收藏
  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onBookmark?.(item.id, !isBookmarked);
  };

  // 处理分享
  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(item);
  };

  // 获取难度配置
  const difficultyConfig = item.difficulty ? DIFFICULTY_CONFIG[item.difficulty] : null;

  return (
    <div
      className={`group bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/knowledge/${item.id}`} className="block p-6">
        {/* 头部信息 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            {/* 标题 */}
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2">
              {renderHighlightedText(item.title, item.highlight?.title)}
            </h3>

            {/* 元信息 */}
            <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
              {/* 分类 */}
              {item.category && (
                <div className="flex items-center gap-1">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.category.color || '#6B7280' }}
                  />
                  <span>{item.category.name}</span>
                </div>
              )}

              {/* 难度 */}
              {difficultyConfig && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${difficultyConfig.color}`}>
                  {difficultyConfig.icon} {difficultyConfig.label}
                </span>
              )}

              {/* 更新时间 */}
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(item.last_updated)}</span>
              </div>

              {/* 浏览次数 */}
              {item.view_count && item.view_count > 0 && (
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{item.view_count}</span>
                </div>
              )}

              {/* 相关性分数 */}
              {showRelevanceScore && item.relevanceScore && (
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4" />
                  <span>相关性: {Math.round(item.relevanceScore)}</span>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          {showActions && isHovered && (
            <div className="flex items-center gap-2 ml-4">
              <button
                type="button"
                onClick={handleBookmark}
                className="p-2 text-gray-400 hover:text-yellow-500 transition-colors"
                title={isBookmarked ? '取消收藏' : '收藏文章'}
              >
                {isBookmarked ? (
                  <Bookmark className="w-5 h-5 text-yellow-500 fill-current" />
                ) : (
                  <Bookmark className="w-5 h-5" />
                )}
              </button>
              
              <button
                type="button"
                onClick={handleShare}
                className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                title="分享文章"
              >
                <Share className="w-5 h-5" />
              </button>
            </div>
          )}
        </div>

        {/* 描述 */}
        {item.description && (
          <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2">
            {renderHighlightedText(item.description, item.highlight?.description)}
          </p>
        )}

        {/* 内容预览 */}
        {item.highlight?.content && (
          <div className="bg-gray-50 border-l-4 border-blue-500 p-3 mb-4">
            <p className="text-sm text-gray-700 leading-relaxed">
              <span 
                dangerouslySetInnerHTML={{ __html: item.highlight.content }}
                className="[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded"
              />
            </p>
          </div>
        )}

        {/* 标签 */}
        {item.tags && item.tags.length > 0 && (
          <div className="flex items-center gap-2 mb-4">
            <Tag className="w-4 h-4 text-gray-400" />
            <div className="flex flex-wrap gap-2">
              {item.tags.slice(0, 5).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full hover:bg-gray-200 transition-colors"
                >
                  {tag}
                </span>
              ))}
              {item.tags.length > 5 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">
                  +{item.tags.length - 5}
                </span>
              )}
            </div>
          </div>
        )}

        {/* 底部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <FileText className="w-4 h-4" />
            <span>知识库文章</span>
          </div>

          <div className="flex items-center gap-1 text-blue-600 group-hover:text-blue-700 transition-colors">
            <span className="text-sm font-medium">查看详情</span>
            <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </div>
        </div>
      </Link>
    </div>
  );
}
