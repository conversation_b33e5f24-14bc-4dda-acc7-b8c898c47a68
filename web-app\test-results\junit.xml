<testsuites id="" name="" tests="17" failures="14" skipped="0" errors="0" time="145.795659">
<testsuite name="knowledge\component-imports-playwright.spec.ts" timestamp="2025-07-03T12:13:52.448Z" hostname="chromium" tests="8" failures="7" skipped="0" time="58.87" errors="0">
<testcase name="组件模块导入验证 › 搜索模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.735">
<failure message="component-imports-playwright.spec.ts:14:7 搜索模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:14:7 › 组件模块导入验证 › 搜索模块组件导入测试 ─────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      29 |     
      30 |     // 访问包含搜索组件的页面
    > 31 |     await page.goto('/knowledge');
         |                ^
      32 |     await page.waitForLoadState('networkidle');
      33 |     
      34 |     // 等待组件加载
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      29 |     
      30 |     // 访问包含搜索组件的页面
    > 31 |     await page.goto('/knowledge');
         |                ^
      32 |     await page.waitForLoadState('networkidle');
      33 |     
      34 |     // 等待组件加载
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 导航模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.835">
<failure message="component-imports-playwright.spec.ts:53:7 导航模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:53:7 › 组件模块导入验证 › 导航模块组件导入测试 ─────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      64 |     });
      65 |     
    > 66 |     await page.goto('/knowledge');
         |                ^
      67 |     await page.waitForLoadState('networkidle');
      68 |     await page.waitForTimeout(3000);
      69 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:66:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      64 |     });
      65 |     
    > 66 |     await page.goto('/knowledge');
         |                ^
      67 |     await page.waitForLoadState('networkidle');
      68 |     await page.waitForTimeout(3000);
      69 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:66:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 展示模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.714">
<failure message="component-imports-playwright.spec.ts:86:7 展示模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:86:7 › 组件模块导入验证 › 展示模块组件导入测试 ─────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


       97 |     });
       98 |     
    >  99 |     await page.goto('/knowledge');
          |                ^
      100 |     await page.waitForLoadState('networkidle');
      101 |     await page.waitForTimeout(3000);
      102 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:99:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


       97 |     });
       98 |     
    >  99 |     await page.goto('/knowledge');
          |                ^
      100 |     await page.waitForLoadState('networkidle');
      101 |     await page.waitForTimeout(3000);
      102 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:99:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 页面模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.67">
<failure message="component-imports-playwright.spec.ts:119:7 页面模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:119:7 › 组件模块导入验证 › 页面模块组件导入测试 ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      131 |     
      132 |     // 测试知识库主页
    > 133 |     await page.goto('/knowledge');
          |                ^
      134 |     await page.waitForLoadState('networkidle');
      135 |     await page.waitForTimeout(2000);
      136 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:133:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      131 |     
      132 |     // 测试知识库主页
    > 133 |     await page.goto('/knowledge');
          |                ^
      134 |     await page.waitForLoadState('networkidle');
      135 |     await page.waitForTimeout(2000);
      136 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:133:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 布局模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.648">
<failure message="component-imports-playwright.spec.ts:153:7 布局模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:153:7 › 组件模块导入验证 › 布局模块组件导入测试 ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      164 |     });
      165 |     
    > 166 |     await page.goto('/knowledge');
          |                ^
      167 |     await page.waitForLoadState('networkidle');
      168 |     await page.waitForTimeout(3000);
      169 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:166:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      164 |     });
      165 |     
    > 166 |     await page.goto('/knowledge');
          |                ^
      167 |     await page.waitForLoadState('networkidle');
      168 |     await page.waitForTimeout(3000);
      169 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:166:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 管理后台模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.661">
<failure message="component-imports-playwright.spec.ts:185:7 管理后台模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:185:7 › 组件模块导入验证 › 管理后台模块组件导入测试 ──────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge
    Call log:
      - navigating to "http://localhost:3000/admin/knowledge", waiting until "load"


      197 |     
      198 |     // 尝试访问管理后台页面
    > 199 |     await page.goto('/admin/knowledge');
          |                ^
      200 |     await page.waitForLoadState('networkidle');
      201 |     await page.waitForTimeout(3000);
      202 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:199:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge
    Call log:
      - navigating to "http://localhost:3000/admin/knowledge", waiting until "load"


      197 |     
      198 |     // 尝试访问管理后台页面
    > 199 |     await page.goto('/admin/knowledge');
          |                ^
      200 |     await page.waitForLoadState('networkidle');
      201 |     await page.waitForTimeout(3000);
      202 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:199:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 工具模块组件导入测试" classname="knowledge\component-imports-playwright.spec.ts" time="6.713">
<failure message="component-imports-playwright.spec.ts:220:7 工具模块组件导入测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\component-imports-playwright.spec.ts:220:7 › 组件模块导入验证 › 工具模块组件导入测试 ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      231 |     });
      232 |     
    > 233 |     await page.goto('/knowledge');
          |                ^
      234 |     await page.waitForLoadState('networkidle');
      235 |     await page.waitForTimeout(3000);
      236 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:233:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      231 |     });
      232 |     
    > 233 |     await page.goto('/knowledge');
          |                ^
      234 |     await page.waitForLoadState('networkidle');
      235 |     await page.waitForTimeout(3000);
      236 |     
        at D:\MysqlAi.De\web-app\src\tests\knowledge\component-imports-playwright.spec.ts:233:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="组件模块导入验证 › 整体模块导入完整性测试" classname="knowledge\component-imports-playwright.spec.ts" time="11.894">
<system-out>
<![CDATA[测试页面: /knowledge
⚠️ 页面 /knowledge 可能需要权限或不存在
测试页面: /admin/knowledge
⚠️ 页面 /admin/knowledge 可能需要权限或不存在
测试页面: /admin/knowledge/articles
⚠️ 页面 /admin/knowledge/articles 可能需要权限或不存在
测试页面: /admin/knowledge/categories
⚠️ 页面 /admin/knowledge/categories 可能需要权限或不存在
✅ 整体模块导入完整性测试通过
📊 测试统计: 0 个错误, 0 个警告
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="knowledge\knowledge-refactor-playwright.spec.ts" timestamp="2025-07-03T12:13:52.448Z" hostname="chromium" tests="9" failures="7" skipped="0" time="47.223" errors="0">
<testcase name="知识库组件重构验证 › 组件模块导入静态验证测试" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="0.375">
<system-out>
<![CDATA[✅ 组件模块导入静态验证测试通过
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 文件结构验证测试" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="0.414">
<system-out>
<![CDATA[✅ 文件结构验证测试完成
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 导航功能测试 (navigation 模块)" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.568">
<failure message="knowledge-refactor-playwright.spec.ts:214:7 导航功能测试 (navigation 模块)" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:214:7 › 知识库组件重构验证 › 导航功能测试 (navigation 模块) 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      213 |
      214 |   test('导航功能测试 (navigation 模块)', async ({ page }) => {
    > 215 |     await page.goto('/knowledge');
          |                ^
      216 |     await page.waitForLoadState('networkidle');
      217 |     
      218 |     // 查找分类导航链接
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:215:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      213 |
      214 |   test('导航功能测试 (navigation 模块)', async ({ page }) => {
    > 215 |     await page.goto('/knowledge');
          |                ^
      216 |     await page.waitForLoadState('networkidle');
      217 |     
      218 |     // 查找分类导航链接
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:215:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 面包屑导航测试 (navigation 模块)" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.578">
<failure message="knowledge-refactor-playwright.spec.ts:243:7 面包屑导航测试 (navigation 模块)" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:243:7 › 知识库组件重构验证 › 面包屑导航测试 (navigation 模块) 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      243 |   test('面包屑导航测试 (navigation 模块)', async ({ page }) => {
      244 |     // 访问一个具体的知识点页面
    > 245 |     await page.goto('/knowledge');
          |                ^
      246 |     await page.waitForLoadState('networkidle');
      247 |     
      248 |     // 查找面包屑导航
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:245:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      243 |   test('面包屑导航测试 (navigation 模块)', async ({ page }) => {
      244 |     // 访问一个具体的知识点页面
    > 245 |     await page.goto('/knowledge');
          |                ^
      246 |     await page.waitForLoadState('networkidle');
      247 |     
      248 |     // 查找面包屑导航
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:245:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 页面布局测试 (layout 模块)" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.727">
<failure message="knowledge-refactor-playwright.spec.ts:260:7 页面布局测试 (layout 模块)" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:260:7 › 知识库组件重构验证 › 页面布局测试 (layout 模块) 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      259 |
      260 |   test('页面布局测试 (layout 模块)', async ({ page }) => {
    > 261 |     await page.goto('/knowledge');
          |                ^
      262 |     await page.waitForLoadState('networkidle');
      263 |     
      264 |     // 验证页面布局结构
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:261:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      259 |
      260 |   test('页面布局测试 (layout 模块)', async ({ page }) => {
    > 261 |     await page.goto('/knowledge');
          |                ^
      262 |     await page.waitForLoadState('networkidle');
      263 |     
      264 |     // 验证页面布局结构
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:261:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 组件导入验证测试" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.504">
<failure message="knowledge-refactor-playwright.spec.ts:282:7 组件导入验证测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:282:7 › 知识库组件重构验证 › 组件导入验证测试 ────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      294 |     });
      295 |     
    > 296 |     await page.goto('/knowledge');
          |                ^
      297 |     await page.waitForLoadState('networkidle');
      298 |     
      299 |     // 等待一段时间让所有组件加载
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:296:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      294 |     });
      295 |     
    > 296 |     await page.goto('/knowledge');
          |                ^
      297 |     await page.waitForLoadState('networkidle');
      298 |     
      299 |     // 等待一段时间让所有组件加载
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:296:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 管理后台组件访问测试 (admin 模块)" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.816">
<failure message="knowledge-refactor-playwright.spec.ts:319:7 管理后台组件访问测试 (admin 模块)" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:319:7 › 知识库组件重构验证 › 管理后台组件访问测试 (admin 模块) 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge
    Call log:
      - navigating to "http://localhost:3000/admin/knowledge", waiting until "load"


      319 |   test('管理后台组件访问测试 (admin 模块)', async ({ page }) => {
      320 |     // 尝试访问管理后台页面
    > 321 |     await page.goto('/admin/knowledge');
          |                ^
      322 |     
      323 |     // 等待页面加载
      324 |     await page.waitForLoadState('networkidle');
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:321:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge
    Call log:
      - navigating to "http://localhost:3000/admin/knowledge", waiting until "load"


      319 |   test('管理后台组件访问测试 (admin 模块)', async ({ page }) => {
      320 |     // 尝试访问管理后台页面
    > 321 |     await page.goto('/admin/knowledge');
          |                ^
      322 |     
      323 |     // 等待页面加载
      324 |     await page.waitForLoadState('networkidle');
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:321:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库组件重构验证 › 性能和加载时间测试" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.596">
<failure message="knowledge-refactor-playwright.spec.ts:353:7 性能和加载时间测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:353:7 › 知识库组件重构验证 › 性能和加载时间测试 ───────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      354 |     const startTime = Date.now();
      355 |     
    > 356 |     await page.goto('/knowledge');
          |                ^
      357 |     await page.waitForLoadState('networkidle');
      358 |     
      359 |     const loadTime = Date.now() - startTime;
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:356:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      354 |     const startTime = Date.now();
      355 |     
    > 356 |     await page.goto('/knowledge');
          |                ^
      357 |     await page.waitForLoadState('networkidle');
      358 |     
      359 |     const loadTime = Date.now() - startTime;
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:356:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="知识库功能完整性测试 › 端到端用户流程测试" classname="knowledge\knowledge-refactor-playwright.spec.ts" time="6.645">
<failure message="knowledge-refactor-playwright.spec.ts:377:7 端到端用户流程测试" type="FAILURE">
<![CDATA[  [chromium] › knowledge\knowledge-refactor-playwright.spec.ts:377:7 › 知识库功能完整性测试 › 端到端用户流程测试 ──────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      379 |     
      380 |     // 1. 访问知识库首页
    > 381 |     await page.goto('/knowledge');
          |                ^
      382 |     await page.waitForLoadState('networkidle');
      383 |     
      384 |     // 2. 使用搜索功能
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:381:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge
    Call log:
      - navigating to "http://localhost:3000/knowledge", waiting until "load"


      379 |     
      380 |     // 1. 访问知识库首页
    > 381 |     await page.goto('/knowledge');
          |                ^
      382 |     await page.waitForLoadState('networkidle');
      383 |     
      384 |     // 2. 使用搜索功能
        at D:\MysqlAi.De\web-app\src\tests\knowledge\knowledge-refactor-playwright.spec.ts:381:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace web-app\test-results\artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\trace.zip]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\video.webm]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\error-context.md]]

[[ATTACHMENT|artifacts\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>