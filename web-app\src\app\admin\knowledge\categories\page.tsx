'use client';

// MySQLAi.de - 知识库分类管理页面
// 使用新的知识库共享组件重构的分类管理功能

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, FolderOpen, AlertCircle, BarChart3 } from 'lucide-react';
import { categoriesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import {
  KnowledgeSearchBar
} from '@/components/admin/knowledge';
import CategoryForm from '@/components/admin/CategoryForm';
import CategoryList from '@/components/admin/CategoryList';
import BatchOperations from '@/components/admin/BatchOperations';
import ConfirmDialog from '@/components/admin/ConfirmDialog';
import type { Database } from '@/lib/database.types';

type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'] & {
  article_count?: number; // API返回的字段名
  articleCount?: number;  // 兼容旧的字段名
};

interface CategoryFormData {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  order_index: number;
}

export default function KnowledgeCategoriesPage() {
  const [categories, setCategories] = useState<KnowledgeCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<KnowledgeCategory | null>(null);
  const [saving, setSaving] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // 批量操作状态
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await categoriesApi.getAll(true); // 包含统计信息
      
      if (response.success) {
        // 按排序索引排序
        const sortedCategories = (response.data || []).sort((a, b) => a.order_index - b.order_index);
        setCategories(sortedCategories);
      } else {
        setError(response.error || '获取分类失败');
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
      setError('获取分类失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchCategories();
  }, []);

  // 处理创建新分类
  const handleCreateCategory = () => {
    setEditingCategory(null);
    setShowForm(true);
    setError('');
  };

  // 处理编辑分类
  const handleEditCategory = (category: KnowledgeCategory) => {
    setEditingCategory(category);
    setShowForm(true);
    setError('');
  };

  // 处理保存分类
  const handleSaveCategory = async (data: CategoryFormData) => {
    try {
      setSaving(true);
      setError('');

      let response;
      if (editingCategory) {
        // 更新现有分类
        response = await categoriesApi.update(editingCategory.id, {
          name: data.name,
          description: data.description || null,
          icon: data.icon,
          color: data.color,
          order_index: data.order_index
        });
      } else {
        // 创建新分类
        response = await categoriesApi.create({
          id: data.id,
          name: data.name,
          description: data.description || null,
          icon: data.icon,
          color: data.color,
          order_index: data.order_index,

        });
      }

      if (response.success) {
        // 保存成功，关闭表单并刷新列表
        setShowForm(false);
        setEditingCategory(null);
        await fetchCategories();
      } else {
        setError(response.error || '保存失败');
      }
    } catch (error) {
      console.error('保存分类失败:', error);
      setError('保存失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 处理取消操作
  const handleCancelForm = () => {
    setShowForm(false);
    setEditingCategory(null);
    setError('');
  };

  // 处理删除分类
  const handleDeleteCategory = async (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    const articleCount = category?.article_count || category?.articleCount || 0;

    if (articleCount > 0) {
      alert(`无法删除分类"${category?.name || '未知分类'}"，该分类下还有 ${articleCount} 篇文章。请先移动或删除这些文章。`);
      return;
    }

    if (!confirm(`确定要删除分类"${category?.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      setDeleteLoading(categoryId);
      const response = await categoriesApi.delete(categoryId);
      
      if (response.success) {
        // 删除成功，刷新列表
        await fetchCategories();
        setSelectedCategories(prev => prev.filter(id => id !== categoryId));
      } else {
        alert('删除失败: ' + (response.error || '未知错误'));
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      alert('删除失败，请稍后重试');
    } finally {
      setDeleteLoading(null);
    }
  };

  // 处理分类重新排序
  const handleReorderCategories = async (reorderedCategories: KnowledgeCategory[]) => {
    try {
      // 更新本地状态
      setCategories(reorderedCategories);

      // 批量更新排序
      const updatePromises = reorderedCategories.map((category, index) =>
        categoriesApi.update(category.id, { order_index: index })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      console.error('更新排序失败:', error);
      // 如果更新失败，重新获取数据
      await fetchCategories();
    }
  };

  // 批量操作处理函数
  const handleSelectCategory = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSelectAll = () => {
    setSelectedCategories(categories.map(category => category.id));
  };

  const handleDeselectAll = () => {
    setSelectedCategories([]);
  };

  // 批量删除分类
  const handleBatchDelete = async () => {
    try {
      setBatchLoading(true);

      // 检查是否有分类包含文章
      const categoriesWithArticles = categories.filter(cat => {
        const articleCount = cat.article_count || cat.articleCount || 0;
        return selectedCategories.includes(cat.id) && articleCount > 0;
      });

      if (categoriesWithArticles.length > 0) {
        const categoryNames = categoriesWithArticles.map(cat => cat.name).join('、');
        alert(`无法删除分类"${categoryNames}"，这些分类下还有文章。请先移动或删除这些文章。`);
        setBatchLoading(false);
        return;
      }

      // 并行删除所有选中的分类
      const deletePromises = selectedCategories.map(categoryId =>
        categoriesApi.delete(categoryId)
      );

      await Promise.all(deletePromises);

      // 删除成功，刷新列表
      await fetchCategories();
      setSelectedCategories([]);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('批量删除失败:', error);
      alert('批量删除失败，请稍后重试');
    } finally {
      setBatchLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 筛选分类
  const filteredCategories = categories.filter(category =>
    !searchQuery || 
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-mysql-text mb-2">
            分类管理
          </h1>
          <p className="text-mysql-text-light text-sm sm:text-base">
            管理知识库分类，支持拖拽排序和批量操作
          </p>
        </div>

        <Button
          variant="primary"
          icon={<Plus className="w-5 h-5" />}
          onClick={handleCreateCategory}
          className="shadow-lg w-full sm:w-auto"
        >
          创建新分类
        </Button>
      </div>

      {/* 搜索栏 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div className="flex-1 lg:mr-4">
          <KnowledgeSearchBar
            placeholder="搜索分类名称或描述..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={handleSearch}
            onClear={() => setSearchQuery('')}
            loading={loading}
            showFilter={false}
          />
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </motion.div>
      )}

      {/* 批量操作 */}
      {selectedCategories.length > 0 && (
        <BatchOperations
          selectedCount={selectedCategories.length}
          onSelectAll={handleSelectAll}
          onDeselectAll={handleDeselectAll}
          onDelete={() => setShowDeleteConfirm(true)}
          loading={batchLoading}
        />
      )}

      {/* 分类统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
              <FolderOpen className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">总分类数</p>
              <p className="text-2xl font-bold text-mysql-text">{categories.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
              <BarChart3 className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">包含文章</p>
              <p className="text-2xl font-bold text-mysql-text">
                {categories.filter(cat => {
                  const articleCount = cat.article_count || cat.articleCount || 0;
                  return articleCount > 0;
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-yellow-100 rounded-lg">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">空分类</p>
              <p className="text-2xl font-bold text-mysql-text">
                {categories.filter(cat => {
                  const articleCount = cat.article_count || cat.articleCount || 0;
                  return articleCount === 0;
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 分类列表 */}
      <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-mysql-text">
            分类列表
          </h2>
          <p className="text-sm text-mysql-text-light">
            共 {filteredCategories.length} 个分类
          </p>
        </div>

        <CategoryList
          categories={filteredCategories}
          onEdit={handleEditCategory}
          onDelete={handleDeleteCategory}
          onReorder={handleReorderCategories}
          loading={loading}
          deleteLoading={deleteLoading}
          selectedCategories={selectedCategories}
          onSelectCategory={handleSelectCategory}
        />
      </div>

      {/* 分类表单对话框 */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <CategoryForm
                category={editingCategory}
                onSave={handleSaveCategory}
                onCancel={handleCancelForm}
                loading={saving}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleBatchDelete}
        title="批量删除分类"
        message={`确定要删除选中的 ${selectedCategories.length} 个分类吗？此操作不可撤销。`}
        confirmText="删除"
        cancelText="取消"
        loading={batchLoading}
        type="danger"
      />
    </div>
  );
}
