'use client';

// MySQLAi.de - KnowledgeCard知识点卡片组件
// 基于FeatureCard组件，专门用于展示MySQL知识点信息

import React from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Clock, 
  Star, 
  ArrowRight, 
  Calendar,
  Tag,
  TrendingUp,
  Database,
  Code,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { KnowledgeCardProps, KnowledgeItem } from '@/lib/types';

// 难度等级配置
const difficultyConfig = {
  beginner: {
    label: '初级',
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: '🟢'
  },
  intermediate: {
    label: '中级', 
    color: 'bg-yellow-100 text-yellow-700 border-yellow-200',
    icon: '🟡'
  },
  advanced: {
    label: '高级',
    color: 'bg-red-100 text-red-700 border-red-200', 
    icon: '🔴'
  }
};

// 分类图标映射
const categoryIcons = {
  'basics': Database,
  'database-operations': Settings,
  'table-operations': Code,
  'data-operations': BookOpen,
  'advanced-queries': TrendingUp,
  'management': Settings
};

// 估算阅读时间（基于内容长度）
const estimateReadingTime = (content: string): number => {
  const wordsPerMinute = 200; // 中文阅读速度约200字/分钟
  const wordCount = content.length;
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
};

// 格式化更新时间
const formatUpdateTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '今天更新';
  if (diffDays <= 7) return `${diffDays}天前更新`;
  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前更新`;
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

const KnowledgeCard = React.forwardRef<HTMLDivElement, KnowledgeCardProps>(({
  item,
  displayMode = 'grid',
  onClick,
  className,
  ...props
}, ref) => {
  const difficulty = difficultyConfig[item.difficulty];
  const CategoryIcon = categoryIcons[(item.category_id || '') as keyof typeof categoryIcons] || BookOpen;
  const readingTime = estimateReadingTime(item.content);
  const updateTime = formatUpdateTime(item.last_updated);

  // 网格模式（详细显示）- grid模式和list模式支持
  if (displayMode === 'grid') {
    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        viewport={{ once: true }}
        className="group h-full"
        {...props}
      >
        <div
          className={cn(
            'relative bg-white rounded-2xl shadow-lg border border-mysql-border h-full',
            'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',
            'cursor-pointer overflow-hidden',
            'hover:ring-2 hover:ring-mysql-primary/20',
            className
          )}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick?.();
          }}
          data-testid="knowledge-card"
        >
          {/* 顶部渐变装饰 */}
          <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-mysql-primary to-mysql-accent" />

          <div className="p-6 h-full flex flex-col">
            {/* 头部：图标、标题和难度 */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center flex-1 min-w-0">
                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-mysql-primary to-mysql-accent shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                  <CategoryIcon className="w-6 h-6 text-white" />
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <h3 className="text-lg font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 line-clamp-2">
                    {item.title}
                  </h3>
                </div>
              </div>
              <div className={cn(
                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',
                difficulty.color
              )}>
                <span className="mr-1">{difficulty.icon}</span>
                {difficulty.label}
              </div>
            </div>

            {/* 描述 */}
            <p className="text-mysql-text-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3">
              {item.description}
            </p>

            {/* 标签云 */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {item.tags.slice(0, 3).map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 text-xs bg-mysql-primary-light text-mysql-primary rounded-md"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
                {item.tags.length > 3 && (
                  <span className="text-xs text-mysql-text-light">
                    +{item.tags.length - 3}
                  </span>
                )}
              </div>
            )}

            {/* 底部信息 */}
            <div className="mt-auto pt-4 border-t border-mysql-border">
              <div className="flex items-center justify-between text-xs text-mysql-text-light mb-3">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  <span>{readingTime}分钟阅读</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>{updateTime}</span>
                </div>
              </div>
              
              {/* 查看详情按钮 */}
              <motion.div
                className="flex items-center justify-between text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-sm font-medium">查看详情</span>
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </div>
          </div>

          {/* 悬停光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
        </div>
      </motion.div>
    );
  }

  // 列表模式（紧凑显示）
  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      viewport={{ once: true }}
      className="group"
      {...props}
    >
      <div
        className={cn(
          'bg-white rounded-xl p-4 shadow-md border border-mysql-border',
          'hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out',
          'cursor-pointer hover:ring-2 hover:ring-mysql-primary/20',
          className
        )}
        onClick={onClick}
        data-testid="knowledge-card"
      >
        <div className="flex items-center space-x-4">
          {/* 图标 */}
          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-mysql-primary to-mysql-accent text-white flex-shrink-0">
            <CategoryIcon className="w-5 h-5" />
          </div>

          {/* 内容 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-base font-semibold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 truncate">
                {item.title}
              </h4>
              <div className={cn(
                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',
                difficulty.color
              )}>
                {difficulty.label}
              </div>
            </div>
            
            <p className="text-mysql-text-light text-sm line-clamp-2 mb-2">
              {item.description}
            </p>

            <div className="flex items-center justify-between text-xs text-mysql-text-light">
              <div className="flex items-center space-x-3">
                <div className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  <span>{readingTime}分钟</span>
                </div>
                <div className="flex items-center">
                  <Tag className="w-3 h-3 mr-1" />
                  <span>{item.tags?.length || 0}个标签</span>
                </div>
              </div>
              <span>{updateTime}</span>
            </div>
          </div>

          {/* 箭头 */}
          <ArrowRight className="w-5 h-5 text-mysql-text-light group-hover:text-mysql-primary transition-colors duration-300 flex-shrink-0" />
        </div>
      </div>
    </motion.div>
  );
});

KnowledgeCard.displayName = 'KnowledgeCard';

export default KnowledgeCard;
