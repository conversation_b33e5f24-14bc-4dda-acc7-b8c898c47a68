'use client';

// MySQLAi.de - RelatedItems相关推荐组件
// 基于知识点的标签和分类实现智能推荐相关内容，提供横向滚动的推荐列表

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Sparkles, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { RelatedItemsProps, KnowledgeItem } from '@/lib/types';
import { getRelatedKnowledgeItems } from '@/lib/knowledge';

const RelatedItems = React.forwardRef<HTMLDivElement, RelatedItemsProps>(({
  currentItem,
  maxItems = 6,
  className,
  ...props
}, ref) => {
  const router = useRouter();
  const [relatedItems, setRelatedItems] = useState<KnowledgeItem[]>([]);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 获取相关推荐内容
  useEffect(() => {
    const fetchRelatedItems = async () => {
      setIsLoading(true);
      try {
        const items = await getRelatedKnowledgeItems(currentItem, maxItems);
        setRelatedItems(items);
      } catch (error) {
        console.error('获取相关推荐失败:', error);
        setRelatedItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedItems();
  }, [currentItem, maxItems]);

  // 检查滚动状态
  const checkScrollButtons = () => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
  };

  // 监听滚动事件
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    checkScrollButtons();
    container.addEventListener('scroll', checkScrollButtons);
    
    // 监听窗口大小变化
    const handleResize = () => {
      setTimeout(checkScrollButtons, 100);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      container.removeEventListener('scroll', checkScrollButtons);
      window.removeEventListener('resize', handleResize);
    };
  }, [relatedItems]);

  // 滚动控制
  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const cardWidth = 320; // 卡片宽度 + 间距
    const scrollAmount = cardWidth * 2; // 每次滚动2个卡片的距离

    const targetScrollLeft = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: targetScrollLeft,
      behavior: 'smooth'
    });
  };

  // 如果没有相关内容，不显示组件
  if (!isLoading && relatedItems.length === 0) {
    return null;
  }

  return (
    <div ref={ref} className={cn('w-full', className)} {...props}>
      {/* 标题区域 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Sparkles className="w-6 h-6 text-mysql-primary mr-3" />
          <h2 className="text-2xl font-bold text-mysql-text">相关推荐</h2>
        </div>
        <div className="text-sm text-mysql-text-light">
          为您推荐 {relatedItems.length} 个相关知识点
        </div>
      </div>

      {/* 加载状态 */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-mysql-primary mr-3"></div>
          <span className="text-mysql-text-light">正在加载推荐内容...</span>
        </div>
      )}

      {/* 推荐内容 */}
      {!isLoading && relatedItems.length > 0 && (
        <div className="relative">
          {/* 左滚动按钮 */}
          <AnimatePresence>
            {canScrollLeft && (
              <motion.button
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                onClick={() => scroll('left')}
                className={cn(
                  'absolute left-0 top-1/2 -translate-y-1/2 z-10',
                  'w-10 h-10 rounded-full bg-white shadow-lg border border-mysql-border',
                  'flex items-center justify-center',
                  'hover:bg-mysql-primary hover:text-white hover:border-mysql-primary',
                  'transition-all duration-200 ease-out',
                  'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
                )}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeft className="w-5 h-5" />
              </motion.button>
            )}
          </AnimatePresence>

          {/* 右滚动按钮 */}
          <AnimatePresence>
            {canScrollRight && (
              <motion.button
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 10 }}
                onClick={() => scroll('right')}
                className={cn(
                  'absolute right-0 top-1/2 -translate-y-1/2 z-10',
                  'w-10 h-10 rounded-full bg-white shadow-lg border border-mysql-border',
                  'flex items-center justify-center',
                  'hover:bg-mysql-primary hover:text-white hover:border-mysql-primary',
                  'transition-all duration-200 ease-out',
                  'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
                )}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronRight className="w-5 h-5" />
              </motion.button>
            )}
          </AnimatePresence>

          {/* 滚动容器 */}
          <div
            ref={scrollContainerRef}
            className={cn(
              'flex space-x-4 overflow-x-auto scrollbar-hide',
              'px-12 -mx-12', // 为滚动按钮留出空间
              'scroll-smooth',
              '[scrollbar-width:none] [&::-webkit-scrollbar]:hidden'
            )}
          >
            {relatedItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ 
                  duration: 0.4, 
                  delay: index * 0.1,
                  ease: "easeOut" 
                }}
                className="flex-shrink-0 w-80"
              >
                <RelatedItemCard
                  item={item}
                  onClick={() => {
                    console.log('🚀 跳转到知识点:', item.id, '分类:', item.category_id, '完整路径:', `/knowledge/${item.category_id}/${item.id}`);
                    // 跳转到知识点详情页面
                    router.push(`/knowledge/${item.category_id}/${item.id}`);
                  }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* 查看更多按钮 */}
      {!isLoading && relatedItems.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
          className="flex justify-center mt-8"
        >
          <button
            type="button"
            className={cn(
              'flex items-center px-6 py-3 rounded-lg',
              'bg-mysql-primary-light text-mysql-primary border border-mysql-primary/20',
              'hover:bg-mysql-primary hover:text-white hover:border-mysql-primary',
              'transition-all duration-200 ease-out',
              'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
            )}
          >
            <span className="mr-2">查看更多相关内容</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </motion.div>
      )}
    </div>
  );
});

// 紧凑版知识点卡片（专门用于推荐列表）
interface RelatedItemCardProps {
  item: KnowledgeItem;
  onClick?: () => void;
}

function RelatedItemCard({ item, onClick }: RelatedItemCardProps) {
  const difficultyConfig = {
    beginner: { label: '初级', color: 'bg-green-100 text-green-700', icon: '🟢' },
    intermediate: { label: '中级', color: 'bg-yellow-100 text-yellow-700', icon: '🟡' },
    advanced: { label: '高级', color: 'bg-red-100 text-red-700', icon: '🔴' }
  };

  const difficulty = difficultyConfig[item.difficulty];

  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group cursor-pointer h-full"
      onClick={onClick}
    >
      <div className={cn(
        'bg-white rounded-xl p-5 shadow-md border border-mysql-border h-full',
        'hover:shadow-xl hover:border-mysql-primary/30 transition-all duration-300',
        'flex flex-col'
      )}>
        {/* 标题和难度 */}
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 line-clamp-2 flex-1">
            {item.title}
          </h3>
          <div className={cn(
            'px-2 py-1 text-xs font-medium rounded-full ml-2 flex-shrink-0',
            difficulty.color
          )}>
            <span className="mr-1">{difficulty.icon}</span>
            {difficulty.label}
          </div>
        </div>

        {/* 描述 */}
        <p className="text-mysql-text-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3">
          {item.description}
        </p>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2 mb-4">
          {item.tags?.slice(0, 2).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 text-xs bg-mysql-primary-light text-mysql-primary rounded"
            >
              {tag}
            </span>
          ))}
          {item.tags && item.tags.length > 2 && (
            <span className="text-xs text-mysql-text-light">
              +{item.tags.length - 2}
            </span>
          )}
        </div>

        {/* 查看详情 */}
        <div className="flex items-center text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300 mt-auto">
          <span className="text-sm font-medium">查看详情</span>
          <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
        </div>
      </div>
    </motion.div>
  );
}

RelatedItems.displayName = 'RelatedItems';

export default RelatedItems;
