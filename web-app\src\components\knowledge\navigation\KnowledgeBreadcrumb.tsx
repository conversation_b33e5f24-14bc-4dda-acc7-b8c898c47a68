'use client';

// MySQLAi.de - 知识库面包屑导航组件
// 提供知识库模块的导航路径显示

import React from 'react';
import Link from 'next/link';
import { usePathname, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ChevronRight,
  Home,
  Database,
  FileText,
  FolderOpen,
  Code,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { generateBreadcrumbs } from '@/lib/knowledge-routes';

// 面包屑项目类型
interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  current?: boolean;
}



export default function KnowledgeBreadcrumb() {
  const pathname = usePathname();
  const params = useParams();

  // 使用新的路由配置生成面包屑
  const breadcrumbItems = generateBreadcrumbs(pathname, params as Record<string, string>);

  // 转换为组件需要的格式
  const breadcrumbs: BreadcrumbItem[] = breadcrumbItems.map((item, index) => ({
    ...item,
    icon: getIconForPath(item.href),
    current: index === breadcrumbItems.length - 1
  }));

  // 根据路径获取对应的图标
  function getIconForPath(href: string): React.ComponentType<{ className?: string }> {
    if (href === '/admin') return Home;
    if (href.includes('/knowledge') && !href.includes('/articles') && !href.includes('/categories') && !href.includes('/code-examples')) return Database;
    if (href.includes('/articles')) return FileText;
    if (href.includes('/categories')) return FolderOpen;
    if (href.includes('/code-examples')) return Code;
    if (href.includes('/stats')) return BarChart3;
    return Database; // 默认图标
  }

  // 如果只有一个面包屑项目，不显示面包屑
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="py-4"
      aria-label="面包屑导航"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((item, index) => (
          <li key={item.href} className="flex items-center">
            {/* 分隔符 */}
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-mysql-text-light mx-2" />
            )}

            {/* 面包屑项目 */}
            {item.current ? (
              <span className="flex items-center space-x-2 text-mysql-primary font-medium">
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </span>
            ) : (
              <Link
                href={item.href}
                className={cn(
                  'flex items-center space-x-2 text-mysql-text-light hover:text-mysql-primary',
                  'transition-colors duration-200'
                )}
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </Link>
            )}
          </li>
        ))}
      </ol>
    </motion.nav>
  );
}
