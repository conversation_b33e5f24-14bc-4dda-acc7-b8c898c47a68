'use client';

// MySQLAi.de - 知识库代码示例管理页面
// 使用新的知识库共享组件重构的代码示例管理功能

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Edit,
  Trash2,
  Code,
  FileText,
  AlertCircle,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { codeExamplesApi, articlesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import CodeBlock from '@/components/ui/CodeBlock';
import {
  KnowledgeSearchBar,
  KnowledgeFilter,
  KnowledgeTable,
  KnowledgePagination
} from '@/components/admin/knowledge';
import CodeExampleForm from '@/components/admin/CodeExampleForm';
import type { Database } from '@/lib/database.types';

type CodeExample = Database['public']['Tables']['code_examples']['Row'] & {
  knowledge_articles?: { title: string };
};
// type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];

interface CodeExampleFormData {
  id: string;
  title: string;
  description: string;
  code: string;
  language: string;
  article_id: string;
  tags: string[];
  order_index: number;
}

// 编程语言标签样式
const LANGUAGE_STYLES: Record<string, string> = {
  sql: 'bg-blue-100 text-blue-800 border-blue-200',
  mysql: 'bg-orange-100 text-orange-800 border-orange-200',
  javascript: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  typescript: 'bg-blue-100 text-blue-800 border-blue-200',
  python: 'bg-green-100 text-green-800 border-green-200',
  java: 'bg-red-100 text-red-800 border-red-200',
  php: 'bg-purple-100 text-purple-800 border-purple-200',
  bash: 'bg-gray-100 text-gray-800 border-gray-200',
  json: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  xml: 'bg-pink-100 text-pink-800 border-pink-200'
};

// 筛选配置
const FILTER_CONFIG = [
  {
    key: 'language',
    label: '编程语言',
    icon: Code,
    options: [] as { label: string; value: string; count?: number }[],
    multiple: false
  },
  {
    key: 'article',
    label: '关联文章',
    icon: FileText,
    options: [] as { label: string; value: string; count?: number }[],
    multiple: false
  }
];

// 表格列配置
const TABLE_COLUMNS = [
  {
    key: 'title',
    title: '代码示例',
    sortable: true,
    render: (value: string, record: CodeExample) => (
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0 w-10 h-10 bg-mysql-primary-light rounded-lg flex items-center justify-center">
          <Code className="w-5 h-5 text-mysql-primary" />
        </div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-mysql-text truncate">
              {value}
            </div>
            <span className={cn(
              'px-2 py-1 text-xs font-medium rounded-full border relative z-0',
              LANGUAGE_STYLES[record.language] || 'bg-gray-100 text-gray-800 border-gray-200'
            )}>
              {record.language.toUpperCase()}
            </span>
          </div>
          {record.description && (
            <div className="text-xs text-mysql-text-light truncate">
              {record.description}
            </div>
          )}
        </div>
      </div>
    )
  },
  {
    key: 'code_preview',
    title: '代码预览',
    width: '300px',
    render: (_value: string, record: CodeExample) => (
      <div className="max-w-xs">
        <CodeBlock
          code={record.code.length > 100 ? record.code.substring(0, 100) + '...' : record.code}
          language={record.language}
          showLineNumbers={false}
          className="text-xs"
        />
      </div>
    )
  },
  {
    key: 'article',
    title: '关联文章',
    width: '150px',
    render: (_value: string, record: CodeExample) => (
      record.knowledge_articles ? (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {record.knowledge_articles.title}
        </span>
      ) : (
        <span className="text-xs text-mysql-text-light">未关联</span>
      )
    )
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: '120px',
    sortable: true,
    render: (value: string) => (
      <div className="text-sm text-mysql-text-light">
        {new Date(value).toLocaleDateString('zh-CN')}
      </div>
    )
  },
  {
    key: 'tags',
    title: '标签',
    width: '150px',
    render: (value: string[] | null) => (
      <div className="flex flex-wrap gap-1">
        {value?.slice(0, 2).map((tag, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800"
          >
            {tag}
          </span>
        ))}
        {value && value.length > 2 && (
          <span className="text-xs text-mysql-text-light">
            +{value.length - 2}
          </span>
        )}
      </div>
    )
  }
];

export default function KnowledgeCodeExamplesPage() {
  const [codeExamples, setCodeExamples] = useState<CodeExample[]>([]);
  // const [articles, setArticles] = useState<Array<{id: string; title: string}>>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCodeExample, setEditingCodeExample] = useState<CodeExample | null>(null);
  const [saving, setSaving] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValues, setFilterValues] = useState<Record<string, string | string[]>>({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({
    key: 'created_at',
    direction: 'desc'
  });

  // 获取代码示例列表
  const fetchCodeExamples = useCallback(async () => {
    try {
      setLoading(true);
      const response = await codeExamplesApi.getAll({
        search: searchQuery || undefined,
        language: filterValues.language as string || undefined,
        articleId: filterValues.article as string || undefined,
        page: currentPage,
        limit: pageSize
      });

      if (response.success) {
        setCodeExamples(response.data || []);
        setTotalCount(response.pagination?.total || 0);
      } else {
        setError(response.error || '获取代码示例失败');
      }
    } catch (error) {
      console.error('获取代码示例列表失败:', error);
      setError('获取代码示例失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, filterValues, currentPage, pageSize]);

  // 获取文章列表
  const fetchArticles = async () => {
    try {
      const response = await articlesApi.getAll({
        includeCodeExamples: false,
        includeRelated: false
      });
      if (response.success) {
        // setArticles(response.data || []);
        // 更新筛选配置中的文章选项
        const articleFilter = FILTER_CONFIG.find(f => f.key === 'article');
        if (articleFilter) {
          articleFilter.options = response.data?.map(article => ({
            label: article.title,
            value: article.id
          })) || [];
        }
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchArticles();
  }, []);

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    setCurrentPage(1);
    fetchCodeExamples();
  }, [searchQuery, filterValues, sortConfig, fetchCodeExamples]);

  // 当页码或页面大小变化时获取数据
  useEffect(() => {
    fetchCodeExamples();
  }, [currentPage, pageSize, fetchCodeExamples]);

  // 更新语言筛选选项
  useEffect(() => {
    const uniqueLanguages = Array.from(new Set(codeExamples.map(ce => ce.language)));
    const languageFilter = FILTER_CONFIG.find(f => f.key === 'language');
    if (languageFilter) {
      languageFilter.options = uniqueLanguages.map(lang => ({
        label: lang.toUpperCase(),
        value: lang
      }));
    }
  }, [codeExamples]);

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 处理筛选
  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilterValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置筛选
  const handleFilterReset = () => {
    setFilterValues({});
  };

  // 处理排序
  const handleSort = (config: { key: string; direction: 'asc' | 'desc' }) => {
    setSortConfig(config);
  };

  // 处理创建新代码示例
  const handleCreateCodeExample = () => {
    setEditingCodeExample(null);
    setShowForm(true);
    setError('');
  };

  // 处理编辑代码示例
  const handleEditCodeExample = (codeExample: CodeExample) => {
    setEditingCodeExample(codeExample);
    setShowForm(true);
    setError('');
  };

  // 处理保存代码示例
  const handleSaveCodeExample = async (data: CodeExampleFormData) => {
    try {
      setSaving(true);
      setError('');

      let response;
      if (editingCodeExample) {
        // 更新现有代码示例
        response = await codeExamplesApi.update(editingCodeExample.id, {
          title: data.title,
          description: data.description || null,
          code: data.code,
          language: data.language,
          article_id: data.article_id || '',

          order_index: data.order_index
        });
      } else {
        // 创建新代码示例
        response = await codeExamplesApi.create({
          id: data.id,
          title: data.title,
          description: data.description || null,
          code: data.code,
          language: data.language,
          article_id: data.article_id || '',

          order_index: data.order_index,

        });
      }

      if (response.success) {
        // 保存成功，关闭表单并刷新列表
        setShowForm(false);
        setEditingCodeExample(null);
        await fetchCodeExamples();
      } else {
        setError(response.error || '保存失败');
      }
    } catch (error) {
      console.error('保存代码示例失败:', error);
      setError('保存失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    setShowForm(false);
    setEditingCodeExample(null);
    setError('');
  };

  // 处理删除代码示例
  const handleDeleteCodeExample = async (codeExampleId: string) => {
    const codeExample = codeExamples.find(ce => ce.id === codeExampleId);
    
    if (!confirm(`确定要删除代码示例"${codeExample?.title}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      setDeleteLoading(codeExampleId);
      const response = await codeExamplesApi.delete(codeExampleId);
      
      if (response.success) {
        // 删除成功，刷新列表
        await fetchCodeExamples();
      } else {
        alert('删除失败: ' + (response.error || '未知错误'));
      }
    } catch (error) {
      console.error('删除代码示例失败:', error);
      alert('删除失败，请稍后重试');
    } finally {
      setDeleteLoading(null);
    }
  };

  // 表格操作配置
  const tableActions = [
    {
      key: 'edit',
      label: '编辑',
      icon: Edit,
      onClick: (record: CodeExample) => {
        handleEditCodeExample(record);
      },
      color: 'primary' as const
    },
    {
      key: 'delete',
      label: '删除',
      icon: Trash2,
      onClick: (record: CodeExample) => {
        handleDeleteCodeExample(record.id);
      },
      color: 'danger' as const,
      disabled: (record: CodeExample) => deleteLoading === record.id
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-mysql-text mb-2">
            代码示例管理
          </h1>
          <p className="text-mysql-text-light text-sm sm:text-base">
            管理知识库代码示例，支持语法高亮和关联文章功能
          </p>
        </div>

        <Button
          variant="primary"
          icon={<Plus className="w-5 h-5" />}
          onClick={handleCreateCodeExample}
          className="shadow-lg w-full sm:w-auto"
          disabled={loading}
        >
          创建新代码示例
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div className="flex-1 lg:mr-4">
          <KnowledgeSearchBar
            placeholder="搜索代码示例标题或内容..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={handleSearch}
            onClear={() => setSearchQuery('')}
            loading={loading}
          />
        </div>
        
        <KnowledgeFilter
          filters={FILTER_CONFIG}
          values={filterValues}
          onChange={handleFilterChange}
          onReset={handleFilterReset}
          isOpen={isFilterOpen}
          onToggle={() => setIsFilterOpen(!isFilterOpen)}
        />
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </motion.div>
      )}

      {/* 代码示例统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
              <Code className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">总示例数</p>
              <p className="text-2xl font-bold text-mysql-text">总计 {totalCount} 个</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
              <FileText className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">关联文章</p>
              <p className="text-2xl font-bold text-mysql-text">
                {codeExamples.filter(ce => ce.knowledge_articles).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-mysql-border p-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg">
              <BarChart3 className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-mysql-text-light">编程语言</p>
              <p className="text-2xl font-bold text-mysql-text">
                编程语言 {Array.from(new Set(codeExamples.map(ce => ce.language))).length} 个
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 代码示例表格 */}
      <KnowledgeTable
        columns={TABLE_COLUMNS}
        data={codeExamples}
        actions={tableActions}
        loading={loading}
        sortConfig={sortConfig}
        onSort={handleSort}
        rowKey="id"
        emptyText={
          searchQuery || Object.keys(filterValues).length > 0
            ? '没有找到匹配的代码示例'
            : '还没有代码示例，开始创建您的第一个代码示例吧'
        }
      />

      {/* 分页 */}
      {totalCount > 0 && (
        <KnowledgePagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          onChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          showSizeChanger={true}
          showQuickJumper={true}
          showTotal={true}
        />
      )}

      {/* 代码示例表单 */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              <CodeExampleForm
                codeExample={editingCodeExample}
                onSave={handleSaveCodeExample}
                onCancel={handleCancel}
                loading={saving}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
