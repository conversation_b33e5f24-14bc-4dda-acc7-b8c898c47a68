'use client';

// MySQLAi.de - ItemPageClient知识点详情页面客户端组件
// 处理知识点详情页面的交互功能、Markdown渲染和相关推荐

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Clock, 
  Tag, 
  BookOpen, 
  Star,
  Calendar,
  User,
  Share2,
  Bookmark,
  ArrowLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { KnowledgeItem } from '@/lib/types';
import Breadcrumb from '@/components/ui/Breadcrumb';
import CodeBlock from '@/components/ui/CodeBlock';
import RelatedItems from '@/components/knowledge/RelatedItems';

interface ItemPageClientProps {
  item: KnowledgeItem;
  categorySlug: string;
  itemSlug: string;
}

export default function ItemPageClient({ 
  item, 
  categorySlug, 
  itemSlug 
}: ItemPageClientProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    // 这里可以添加书签状态的检查逻辑
  }, []);

  // 难度配置
  const difficultyConfig = {
    beginner: { label: '初级', color: 'text-green-600', bgColor: 'bg-green-100', icon: '🟢' },
    intermediate: { label: '中级', color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: '🟡' },
    advanced: { label: '高级', color: 'text-red-600', bgColor: 'bg-red-100', icon: '🔴' }
  };

  const difficulty = difficultyConfig[item.difficulty];

  // 处理分享
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: item.title,
          text: item.description || '',
          url: window.location.href,
        });
      } catch (error) {
        console.log('分享取消或失败');
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      // 这里可以添加提示消息
    }
  };

  // 处理书签
  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // 这里可以添加书签保存逻辑
  };

  // 处理内联Markdown格式
  const renderInlineMarkdown = (text: string): React.ReactNode => {
    // 处理**bold**格式
    let result = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    // 处理*italic*格式
    result = result.replace(/\*(.*?)\*/g, '<em>$1</em>');
    // 处理`code`格式
    result = result.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>');

    return <span dangerouslySetInnerHTML={{ __html: result }} />;
  };

  // 渲染表格
  const renderTable = (tableLines: string[], index: number): React.ReactNode => {
    if (tableLines.length < 2) return null;

    // 解析表格行
    const rows = tableLines.map(line =>
      line.split('|')
        .slice(1, -1) // 移除首尾空元素
        .map(cell => cell.trim())
    );

    // 过滤掉分隔行（包含 --- 的行）
    const headerRow = rows[0];
    const dataRows = rows.slice(1).filter(row =>
      !row.some(cell => cell.includes('---'))
    );

    if (!headerRow || dataRows.length === 0) return null;

    return (
      <div key={`table-${index}`} className="my-6 overflow-x-auto">
        <table className="min-w-full bg-white border border-mysql-border rounded-lg shadow-sm">
          <thead className="bg-mysql-primary-light">
            <tr>
              {headerRow.map((header, headerIndex) => (
                <th
                  key={headerIndex}
                  className="px-4 py-3 text-left text-sm font-semibold text-mysql-primary border-b border-mysql-border"
                >
                  {renderInlineMarkdown(header)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {dataRows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className="hover:bg-mysql-primary-light/30 transition-colors duration-200"
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-3 text-sm text-mysql-text border-b border-mysql-border/50"
                  >
                    {renderInlineMarkdown(cell)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染Markdown内容
  const renderMarkdownContent = (content: string) => {
    // 简单的Markdown渲染 - 处理标题、段落、代码块、表格等
    const lines = content.split('\n');
    const elements: React.ReactNode[] = [];
    let currentCodeBlock = '';
    let currentCodeLanguage = '';
    let inCodeBlock = false;
    let currentList: string[] = [];
    let inList = false;
    let currentTable: string[] = [];
    let inTable = false;

    lines.forEach((line, index) => {
      // 代码块处理
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          // 结束代码块
          elements.push(
            <CodeBlock
              key={`code-${index}`}
              code={currentCodeBlock.trim()}
              language={currentCodeLanguage}
              className="my-6"
            />
          );
          currentCodeBlock = '';
          currentCodeLanguage = '';
          inCodeBlock = false;
        } else {
          // 开始代码块
          currentCodeLanguage = line.replace('```', '').trim() || 'text';
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        currentCodeBlock += line + '\n';
        return;
      }

      // 列表处理
      if (line.startsWith('- ') || line.startsWith('* ')) {
        if (!inList) {
          inList = true;
          currentList = [];
        }
        currentList.push(line.substring(2));
        return;
      } else if (inList) {
        // 结束列表
        elements.push(
          <ul key={`list-${index}`} className="list-disc list-inside my-4 space-y-2">
            {currentList.map((listItem, listIndex) => (
              <li key={listIndex} className="text-mysql-text">{renderInlineMarkdown(listItem)}</li>
            ))}
          </ul>
        );
        currentList = [];
        inList = false;
      }

      // 表格处理 - 检测包含|的行
      if (line.includes('|') && line.trim().length > 0) {
        if (!inTable) {
          inTable = true;
          currentTable = [];
        }
        currentTable.push(line);
        return;
      } else if (inTable) {
        // 结束表格
        elements.push(renderTable(currentTable, index));
        currentTable = [];
        inTable = false;
      }

      // 标题处理
      if (line.startsWith('# ')) {
        elements.push(
          <h1 key={index} className="text-3xl font-bold text-mysql-text mt-8 mb-4">
            {line.substring(2)}
          </h1>
        );
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={index} className="text-2xl font-semibold text-mysql-text mt-6 mb-3">
            {line.substring(3)}
          </h2>
        );
      } else if (line.startsWith('### ')) {
        elements.push(
          <h3 key={index} className="text-xl font-medium text-mysql-text mt-4 mb-2">
            {line.substring(4)}
          </h3>
        );
      } else if (line.trim() === '') {
        // 空行
        elements.push(<div key={index} className="h-2" />);
      } else if (line.trim()) {
        // 普通段落
        elements.push(
          <p key={index} className="text-mysql-text leading-relaxed mb-4">
            {line}
          </p>
        );
      }
    });

    // 处理最后的列表
    if (inList && currentList.length > 0) {
      elements.push(
        <ul key="final-list" className="list-disc list-inside my-4 space-y-2">
          {currentList.map((listItem, listIndex) => (
            <li key={listIndex} className="text-mysql-text">{renderInlineMarkdown(listItem)}</li>
          ))}
        </ul>
      );
    }

    // 处理最后的表格
    if (inTable && currentTable.length > 0) {
      elements.push(renderTable(currentTable, elements.length));
    }

    return elements;
  };

  return (
    <div className="flex flex-col min-h-full bg-white">
      {/* 面包屑导航 */}
      <div className="flex-shrink-0 border-b border-mysql-border bg-white">
        <div className="px-6 py-4">
          <Breadcrumb pathname={`/knowledge/${categorySlug}/${itemSlug}`} />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-6 py-8">
          {/* 知识点头部信息 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-8"
          >
            {/* 标题和操作按钮 */}
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-mysql-text mb-3">
                  {item.title}
                </h1>
                <p className="text-lg text-mysql-text-light mb-4">
                  {item.description}
                </p>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-2 ml-6">
                <button
                  type="button"
                  onClick={handleBookmark}
                  className={cn(
                    'p-2 rounded-lg transition-colors duration-200',
                    isBookmarked 
                      ? 'bg-mysql-primary text-white' 
                      : 'bg-mysql-primary-light text-mysql-primary hover:bg-mysql-primary hover:text-white'
                  )}
                  aria-label="收藏"
                >
                  <Bookmark className="w-5 h-5" />
                </button>
                <button
                  type="button"
                  onClick={handleShare}
                  className="p-2 rounded-lg bg-mysql-primary-light text-mysql-primary hover:bg-mysql-primary hover:text-white transition-colors duration-200"
                  aria-label="分享"
                >
                  <Share2 className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* 元信息 */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-mysql-text-light">
              <div className="flex items-center">
                <span className={cn('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', difficulty.bgColor, difficulty.color)}>
                  <span className="mr-1">{difficulty.icon}</span>
                  {difficulty.label}
                </span>
              </div>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                <span>更新于 {item.last_updated}</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                <span>预计阅读 {Math.ceil(item.content.length / 500)} 分钟</span>
              </div>
            </div>

            {/* 标签 */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-4">
                {item.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-mysql-primary-light text-mysql-primary"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </motion.div>

          {/* 知识点内容 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="prose prose-mysql max-w-none mb-12"
          >
            <div className="bg-white rounded-lg border border-mysql-border p-8">
              {renderMarkdownContent(item.content)}
            </div>
          </motion.div>

          {/* 代码示例 - 暂时禁用，需要单独API调用 */}
          {false && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              className="mb-12"
            >
              <h2 className="text-2xl font-semibold text-mysql-text mb-6">代码示例</h2>
              <div className="space-y-6">
                {/* 代码示例暂时禁用 - 需要单独的API调用来获取 */}
                <div className="text-center py-8 text-mysql-text-light">
                  代码示例功能开发中...
                </div>
              </div>
            </motion.div>
          )}

          {/* 相关推荐 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
          >
            <RelatedItems currentItem={item} maxItems={6} />
          </motion.div>
        </div>
      </div>
    </div>
  );
}
