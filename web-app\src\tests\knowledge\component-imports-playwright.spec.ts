import { test, expect } from '@playwright/test';

/**
 * MySQLAi.de 知识库组件导入验证测试
 * 专门验证重构后的模块化导入是否正常工作
 */

test.describe('组件模块导入验证', () => {
  test.beforeEach(async ({ page }) => {
    // 设置页面超时
    page.setDefaultTimeout(30000);
  });

  test('搜索模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warning') {
        warnings.push(`Console Warning: ${msg.text()}`);
      }
    });
    
    // 访问包含搜索组件的页面
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    
    // 等待组件加载
    await page.waitForTimeout(3000);
    
    // 检查搜索相关的错误
    const searchErrors = errors.filter(error => 
      error.toLowerCase().includes('search') ||
      error.toLowerCase().includes('intelligent') ||
      error.toLowerCase().includes('suggestion')
    );
    
    if (searchErrors.length > 0) {
      console.log('❌ 搜索模块导入错误:');
      searchErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`搜索模块导入失败: ${searchErrors.join(', ')}`);
    }
    
    console.log('✅ 搜索模块组件导入成功');
  });

  test('导航模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查导航相关的错误
    const navErrors = errors.filter(error => 
      error.toLowerCase().includes('navigation') ||
      error.toLowerCase().includes('sidebar') ||
      error.toLowerCase().includes('breadcrumb')
    );
    
    if (navErrors.length > 0) {
      console.log('❌ 导航模块导入错误:');
      navErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`导航模块导入失败: ${navErrors.join(', ')}`);
    }
    
    console.log('✅ 导航模块组件导入成功');
  });

  test('展示模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查展示相关的错误
    const displayErrors = errors.filter(error => 
      error.toLowerCase().includes('card') ||
      error.toLowerCase().includes('related') ||
      error.toLowerCase().includes('display')
    );
    
    if (displayErrors.length > 0) {
      console.log('❌ 展示模块导入错误:');
      displayErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`展示模块导入失败: ${displayErrors.join(', ')}`);
    }
    
    console.log('✅ 展示模块组件导入成功');
  });

  test('页面模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    // 测试知识库主页
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // 检查页面相关的错误
    const pageErrors = errors.filter(error => 
      error.toLowerCase().includes('page') ||
      error.toLowerCase().includes('client') ||
      error.toLowerCase().includes('knowledge')
    );
    
    if (pageErrors.length > 0) {
      console.log('❌ 页面模块导入错误:');
      pageErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`页面模块导入失败: ${pageErrors.join(', ')}`);
    }
    
    console.log('✅ 页面模块组件导入成功');
  });

  test('布局模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查布局相关的错误
    const layoutErrors = errors.filter(error => 
      error.toLowerCase().includes('layout') ||
      error.toLowerCase().includes('wrapper')
    );
    
    if (layoutErrors.length > 0) {
      console.log('❌ 布局模块导入错误:');
      layoutErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`布局模块导入失败: ${layoutErrors.join(', ')}`);
    }
    
    console.log('✅ 布局模块组件导入成功');
  });

  test('管理后台模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    // 尝试访问管理后台页面
    await page.goto('/admin/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查管理后台相关的错误
    const adminErrors = errors.filter(error => 
      error.toLowerCase().includes('admin') ||
      error.toLowerCase().includes('filter') ||
      error.toLowerCase().includes('table') ||
      error.toLowerCase().includes('pagination')
    );
    
    if (adminErrors.length > 0) {
      console.log('❌ 管理后台模块导入错误:');
      adminErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`管理后台模块导入失败: ${adminErrors.join(', ')}`);
    }
    
    console.log('✅ 管理后台模块组件导入成功');
  });

  test('工具模块组件导入测试', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      }
    });
    
    await page.goto('/knowledge');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查工具相关的错误
    const utilErrors = errors.filter(error => 
      error.toLowerCase().includes('utils') ||
      error.toLowerCase().includes('redirect') ||
      error.toLowerCase().includes('route')
    );
    
    if (utilErrors.length > 0) {
      console.log('❌ 工具模块导入错误:');
      utilErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`工具模块导入失败: ${utilErrors.join(', ')}`);
    }
    
    console.log('✅ 工具模块组件导入成功');
  });

  test('整体模块导入完整性测试', async ({ page }) => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(`Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warning') {
        warnings.push(`Console Warning: ${msg.text()}`);
      }
    });
    
    // 访问多个页面进行全面测试
    const testPages = [
      '/knowledge',
      '/admin/knowledge',
      '/admin/knowledge/articles',
      '/admin/knowledge/categories'
    ];
    
    for (const testPage of testPages) {
      console.log(`测试页面: ${testPage}`);
      
      try {
        await page.goto(testPage);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
      } catch (error) {
        console.log(`⚠️ 页面 ${testPage} 可能需要权限或不存在`);
      }
    }
    
    // 过滤出模块导入相关的错误
    const moduleErrors = errors.filter(error => 
      error.includes('import') || 
      error.includes('module') || 
      error.includes('Cannot resolve') ||
      error.includes('Failed to fetch') ||
      error.includes('Unexpected token') ||
      error.includes('SyntaxError')
    );
    
    if (moduleErrors.length > 0) {
      console.log('❌ 发现模块导入错误:');
      moduleErrors.forEach(error => console.log(`  - ${error}`));
      throw new Error(`模块导入完整性测试失败: ${moduleErrors.length} 个错误`);
    }
    
    console.log('✅ 整体模块导入完整性测试通过');
    console.log(`📊 测试统计: ${errors.length} 个错误, ${warnings.length} 个警告`);
  });
});