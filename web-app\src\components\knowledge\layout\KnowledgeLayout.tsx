'use client';

// MySQLAi.de - 知识库布局组件
// 提供知识库模块的统一布局结构

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import KnowledgeBreadcrumb from './KnowledgeBreadcrumb';

interface KnowledgeLayoutProps {
  children: React.ReactNode;
  showBreadcrumb?: boolean;
  fullWidth?: boolean; // 新增：是否使用全宽度布局（用于管理后台）
}

export default function KnowledgeLayout({
  children,
  showBreadcrumb = true,
  fullWidth = false
}: KnowledgeLayoutProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen bg-gray-50"
    >
      {/* 面包屑导航 */}
      {showBreadcrumb && (
        <div className="bg-white border-b border-mysql-border">
          <div className={cn(
            fullWidth ? "px-6" : "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
          )}>
            <KnowledgeBreadcrumb />
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className={cn(
        "py-6",
        fullWidth ? "px-6" : "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
      )}>
        {children}
      </div>
    </motion.div>
  );
}
