'use client';

// MySQLAi.de - 搜索历史界面组件
// 提供搜索历史显示、管理和快速重新搜索功能

import React, { useState } from 'react';
import { useSearchHistory } from '@/hooks/useSearchHistory';
import {
  Clock,
  TrendingUp,
  X,
  Trash2,
  RotateCcw,
  BarChart3
} from 'lucide-react';

// 组件属性
interface SearchHistoryProps {
  onSearchSelect: (query: string) => void;
  onClose?: () => void;
  className?: string;
  showStats?: boolean;
  maxRecentItems?: number;
  maxPopularItems?: number;
}

// 历史项组件属性
interface HistoryItemProps {
  query: string;
  count?: number;
  timestamp?: number;
  onSelect: (query: string) => void;
  onRemove: (query: string) => void;
  showCount?: boolean;
}

// 历史项组件
function HistoryItem({
  query,
  count,
  timestamp,
  onSelect,
  onRemove,
  showCount = false
}: HistoryItemProps) {
  const [isHovered, setIsHovered] = useState(false);

  const formatTimestamp = (ts?: number) => {
    if (!ts) return '';
    const date = new Date(ts);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    return `${Math.floor(diffDays / 30)}月前`;
  };

  return (
    <div
      className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <button
        type="button"
        onClick={() => onSelect(query)}
        className="flex-1 flex items-center gap-3 text-left min-w-0"
      >
        <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate">
            {query}
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
            {timestamp && (
              <span>{formatTimestamp(timestamp)}</span>
            )}
            {showCount && count && count > 1 && (
              <>
                <span>•</span>
                <span>{count}次搜索</span>
              </>
            )}
          </div>
        </div>
      </button>

      {isHovered && (
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            onRemove(query);
          }}
          className="p-1 text-gray-400 hover:text-red-500 transition-colors duration-150"
          title="删除此搜索记录"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}

export default function SearchHistory({
  onSearchSelect,
  onClose,
  className = '',
  showStats = true,
  maxRecentItems = 8,
  maxPopularItems = 5
}: SearchHistoryProps) {
  const {
    recentHistory,
    popularHistory,
    totalSearches,
    uniqueSearches,
    removeFromHistory,
    clearHistory,
    getSearchCount
  } = useSearchHistory();

  const [activeTab, setActiveTab] = useState<'recent' | 'popular'>('recent');
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 处理搜索选择
  const handleSearchSelect = (query: string) => {
    onSearchSelect(query);
    onClose?.();
  };

  // 处理删除单个记录
  const handleRemoveItem = (query: string) => {
    removeFromHistory(query);
  };

  // 处理清空所有记录
  const handleClearAll = () => {
    if (showClearConfirm) {
      clearHistory();
      setShowClearConfirm(false);
      onClose?.();
    } else {
      setShowClearConfirm(true);
    }
  };

  // 取消清空确认
  const handleCancelClear = () => {
    setShowClearConfirm(false);
  };

  const hasHistory = recentHistory.length > 0 || popularHistory.length > 0;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">搜索历史</h3>
        <div className="flex items-center gap-2">
          {hasHistory && (
            <button
              type="button"
              onClick={handleClearAll}
              className={`px-3 py-1 text-sm rounded-md transition-colors duration-150 ${
                showClearConfirm
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'text-gray-500 hover:text-red-600 hover:bg-red-50'
              }`}
            >
              {showClearConfirm ? (
                <div className="flex items-center gap-2">
                  <span>确认清空?</span>
                  <button
                    type="button"
                    onClick={handleCancelClear}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    取消
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Trash2 className="w-4 h-4" />
                  <span>清空</span>
                </div>
              )}
            </button>
          )}
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-150"
              title="关闭搜索历史"
              aria-label="关闭搜索历史"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      {/* 统计信息 */}
      {showStats && hasHistory && (
        <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <BarChart3 className="w-4 h-4" />
              <span>总搜索: {totalSearches}次</span>
            </div>
            <div className="flex items-center gap-1">
              <RotateCcw className="w-4 h-4" />
              <span>不同查询: {uniqueSearches}个</span>
            </div>
          </div>
        </div>
      )}

      {/* 标签页 */}
      {hasHistory && (
        <div className="flex border-b border-gray-200">
          <button
            type="button"
            onClick={() => setActiveTab('recent')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${
              activeTab === 'recent'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Clock className="w-4 h-4" />
              <span>最近搜索</span>
              {recentHistory.length > 0 && (
                <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {recentHistory.length}
                </span>
              )}
            </div>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('popular')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${
              activeTab === 'popular'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <TrendingUp className="w-4 h-4" />
              <span>热门搜索</span>
              {popularHistory.length > 0 && (
                <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {popularHistory.length}
                </span>
              )}
            </div>
          </button>
        </div>
      )}

      {/* 内容区域 */}
      <div className="p-4">
        {!hasHistory ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">暂无搜索历史</p>
            <p className="text-gray-400 text-xs mt-1">开始搜索后，历史记录会显示在这里</p>
          </div>
        ) : (
          <div className="space-y-1">
            {activeTab === 'recent' && (
              <>
                {recentHistory.slice(0, maxRecentItems).map((query, index) => (
                  <HistoryItem
                    key={`recent-${query}-${index}`}
                    query={query}
                    count={getSearchCount(query)}
                    onSelect={handleSearchSelect}
                    onRemove={handleRemoveItem}
                    showCount={true}
                  />
                ))}
                {recentHistory.length > maxRecentItems && (
                  <div className="text-center py-2">
                    <span className="text-xs text-gray-500">
                      还有 {recentHistory.length - maxRecentItems} 条记录...
                    </span>
                  </div>
                )}
              </>
            )}

            {activeTab === 'popular' && (
              <>
                {popularHistory.length === 0 ? (
                  <div className="text-center py-4">
                    <TrendingUp className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">暂无热门搜索</p>
                    <p className="text-gray-400 text-xs mt-1">多次搜索相同内容后会显示在这里</p>
                  </div>
                ) : (
                  popularHistory.slice(0, maxPopularItems).map((query, index) => (
                    <HistoryItem
                      key={`popular-${query}-${index}`}
                      query={query}
                      count={getSearchCount(query)}
                      onSelect={handleSearchSelect}
                      onRemove={handleRemoveItem}
                      showCount={true}
                    />
                  ))
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
