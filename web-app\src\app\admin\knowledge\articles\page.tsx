'use client';

// MySQLAi.de - 知识库文章管理页面
// 使用新的知识库共享组件重构的文章管理功能

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  FileText,
  FolderOpen,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { articlesApi, categoriesApi } from '@/lib/api/knowledge';
import { KNOWLEDGE_ROUTES } from '@/lib/knowledge-routes';
import Button from '@/components/ui/Button';
import {
  KnowledgeSearchBar,
  KnowledgeFilter,
  KnowledgeTable,
  KnowledgePagination
} from '@/components/admin/knowledge';
import BatchOperations from '@/components/admin/BatchOperations';
import ConfirmDialog from '@/components/admin/ConfirmDialog';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];
// type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];

// 筛选配置
const FILTER_CONFIG = [
  {
    key: 'category',
    label: '分类筛选',
    icon: FolderOpen,
    options: [] as { label: string; value: string; count?: number }[],
    multiple: false
  },
  {
    key: 'difficulty',
    label: '难度等级',
    icon: BarChart3,
    options: [
      { label: '初级', value: 'beginner' },
      { label: '中级', value: 'intermediate' },
      { label: '高级', value: 'advanced' }
    ],
    multiple: false
  },
  {
    key: 'status',
    label: '发布状态',
    icon: FileText,
    options: [
      { label: '已发布', value: 'published' },
      { label: '草稿', value: 'draft' }
    ],
    multiple: false
  }
];

// 表格列配置
const TABLE_COLUMNS = [
  {
    key: 'title',
    title: '文章标题',
    width: '300px', // 固定宽度，防止过长标题影响其他列
    sortable: true,
    render: (value: string, record: KnowledgeArticle) => (
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0 w-10 h-10 bg-mysql-primary-light rounded-lg flex items-center justify-center">
          <FileText className="w-5 h-5 text-mysql-primary" />
        </div>
        <div className="min-w-0 flex-1">
          <div className="text-sm font-medium text-mysql-text truncate" title={value}>
            {value}
          </div>
          {record.description && (
            <div className="text-xs text-mysql-text-light truncate" title={record.description}>
              {record.description}
            </div>
          )}
        </div>
      </div>
    )
  },
  {
    key: 'category',
    title: '分类',
    width: '120px',
    render: (_value: string | null, record: KnowledgeArticle) => (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        {record.category_id || '未分类'}
      </span>
    )
  },
  {
    key: 'difficulty',
    title: '难度',
    width: '100px',
    render: (value: string) => {
      const difficultyConfig = {
        beginner: { label: '初级', color: 'bg-green-100 text-green-800' },
        intermediate: { label: '中级', color: 'bg-yellow-100 text-yellow-800' },
        advanced: { label: '高级', color: 'bg-red-100 text-red-800' }
      };
      const config = difficultyConfig[value as keyof typeof difficultyConfig] || difficultyConfig.beginner;
      
      return (
        <span className={cn('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', config.color)}>
          {config.label}
        </span>
      );
    }
  },
  {
    key: 'last_updated',
    title: '更新时间',
    width: '120px',
    sortable: true,
    render: (value: string) => (
      <div className="text-sm text-mysql-text-light">
        {new Date(value).toLocaleDateString('zh-CN')}
      </div>
    )
  },
  {
    key: 'tags',
    title: '标签',
    width: '150px',
    render: (value: string[] | null) => (
      <div className="flex flex-wrap gap-1">
        {value?.slice(0, 2).map((tag, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800"
          >
            {tag}
          </span>
        ))}
        {value && value.length > 2 && (
          <span className="text-xs text-mysql-text-light">
            +{value.length - 2}
          </span>
        )}
      </div>
    )
  }
];

export default function KnowledgeArticlesPage() {
  const [articles, setArticles] = useState<KnowledgeArticle[]>([]);
  // const [categories, setCategories] = useState<Array<{id: string; name: string}>>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValues, setFilterValues] = useState<Record<string, string | string[]>>({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({
    key: 'last_updated',
    direction: 'desc'
  });

  // 批量操作状态
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 获取文章列表
  const fetchArticles = useCallback(async () => {
    try {
      setLoading(true);
      const response = await articlesApi.getAll({
        search: searchQuery || undefined,
        category: filterValues.category as string || undefined,
        difficulty: filterValues.difficulty as string || undefined,
        page: currentPage,
        limit: pageSize,
        includeCodeExamples: false,
        includeRelated: false
      });

      if (response.success) {
        setArticles(response.data || []);
        setTotalCount(response.pagination?.total || 0);
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, filterValues, currentPage, pageSize]);

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await categoriesApi.getAll();
      if (response.success) {
        // setCategories(response.data || []);
        // 更新筛选配置中的分类选项
        const categoryFilter = FILTER_CONFIG.find(f => f.key === 'category');
        if (categoryFilter) {
          categoryFilter.options = response.data?.map(cat => ({
            label: cat.name,
            value: cat.id
          })) || [];
        }
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchCategories();
  }, []);

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    setCurrentPage(1);
    fetchArticles();
  }, [searchQuery, filterValues, sortConfig, fetchArticles]);

  // 当页码或页面大小变化时获取数据
  useEffect(() => {
    fetchArticles();
  }, [currentPage, pageSize, fetchArticles]);

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 处理筛选
  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilterValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置筛选
  const handleFilterReset = () => {
    setFilterValues({});
  };

  // 处理排序
  const handleSort = (config: { key: string; direction: 'asc' | 'desc' }) => {
    setSortConfig(config);
  };

  // 处理选择变化
  const handleSelectionChange = (keys: string[]) => {
    setSelectedArticles(keys);
  };

  // 处理删除文章
  const handleDeleteArticle = async (articleId: string) => {
    try {
      const response = await articlesApi.delete(articleId);
      if (response.success) {
        await fetchArticles();
        setSelectedArticles(prev => prev.filter(id => id !== articleId));
      }
    } catch (error) {
      console.error('删除文章失败:', error);
    }
  };

  // 批量删除文章
  const handleBatchDelete = async () => {
    try {
      setBatchLoading(true);
      await Promise.all(selectedArticles.map(id => articlesApi.delete(id)));
      await fetchArticles();
      setSelectedArticles([]);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('批量删除失败:', error);
    } finally {
      setBatchLoading(false);
    }
  };

  // 表格操作配置
  const tableActions = [
    {
      key: 'preview',
      label: '预览',
      icon: Eye,
      onClick: (record: KnowledgeArticle) => {
        window.open(KNOWLEDGE_ROUTES.ARTICLES.PREVIEW(record.id), '_blank');
      },
      color: 'primary' as const
    },
    {
      key: 'edit',
      label: '编辑',
      icon: Edit,
      onClick: (record: KnowledgeArticle) => {
        window.location.href = KNOWLEDGE_ROUTES.ARTICLES.EDIT(record.id);
      },
      color: 'primary' as const
    },
    {
      key: 'delete',
      label: '删除',
      icon: Trash2,
      onClick: (record: KnowledgeArticle) => {
        handleDeleteArticle(record.id);
      },
      color: 'danger' as const
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-mysql-text mb-2">
            文章管理
          </h1>
          <p className="text-mysql-text-light text-sm sm:text-base">
            管理知识库文章，支持创建、编辑、删除和预览功能
          </p>
        </div>

        <Link href="/admin/knowledge/articles/new">
          <Button
            variant="primary"
            icon={<Plus className="w-5 h-5" />}
            className="shadow-lg w-full sm:w-auto"
          >
            创建新文章
          </Button>
        </Link>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div className="flex-1 lg:mr-4">
          <KnowledgeSearchBar
            placeholder="搜索文章标题、内容或标签..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={handleSearch}
            onClear={() => setSearchQuery('')}
            loading={loading}
          />
        </div>
        
        <KnowledgeFilter
          filters={FILTER_CONFIG}
          values={filterValues}
          onChange={handleFilterChange}
          onReset={handleFilterReset}
          isOpen={isFilterOpen}
          onToggle={() => setIsFilterOpen(!isFilterOpen)}
        />
      </div>

      {/* 批量操作 */}
      {selectedArticles.length > 0 && (
        <BatchOperations
          selectedCount={selectedArticles.length}
          onDelete={() => setShowDeleteConfirm(true)}
          loading={batchLoading}
        />
      )}

      {/* 文章表格 */}
      <KnowledgeTable
        columns={TABLE_COLUMNS}
        data={articles}
        actions={tableActions}
        loading={loading}
        selectable={true}
        selectedKeys={selectedArticles}
        onSelectionChange={handleSelectionChange}
        sortConfig={sortConfig}
        onSort={handleSort}
        rowKey="id"
        emptyText={
          searchQuery || Object.keys(filterValues).length > 0
            ? '没有找到匹配的文章'
            : '还没有文章，开始创建您的第一篇知识库文章吧'
        }
      />

      {/* 分页 */}
      {totalCount > 0 && (
        <KnowledgePagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          onChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          showSizeChanger={true}
          showQuickJumper={true}
          showTotal={true}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleBatchDelete}
        title="批量删除文章"
        message={`确定要删除选中的 ${selectedArticles.length} 篇文章吗？此操作不可撤销。`}
        confirmText="删除"
        cancelText="取消"
        loading={batchLoading}
        type="danger"
      />
    </div>
  );
}
