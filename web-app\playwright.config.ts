import { defineConfig, devices } from '@playwright/test';

/**
 * MySQLAi.de Playwright 测试配置
 * 用于知识库管理系统的端到端测试
 */
export default defineConfig({
  // 测试目录
  testDir: './src/tests',
  
  // 测试文件匹配模式
  testMatch: '**/*playwright*.spec.ts',
  
  // 全局超时设置
  timeout: 60000,
  expect: {
    timeout: 10000,
  },
  
  // 并行执行设置
  fullyParallel: false, // 知识库测试需要顺序执行
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 1, // 单线程执行避免数据冲突
  
  // 报告配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/test-results.json' }],
    ['list'],
    ['junit', { outputFile: 'test-results/junit.xml' }]
  ],
  
  // 输出目录
  outputDir: 'test-results/artifacts',
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: 'http://localhost:3000',
    
    // 浏览器设置
    headless: false, // 显示浏览器窗口便于调试
    viewport: { width: 1280, height: 720 },
    
    // 操作设置
    actionTimeout: 15000,
    navigationTimeout: 30000,
    
    // 截图和录制
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    
    // 其他设置
    ignoreHTTPSErrors: true,
    locale: 'zh-CN',
    timezoneId: 'Asia/Shanghai',
  },

  // 项目配置 - 不同浏览器的测试
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // 知识库测试专用设置
        launchOptions: {
          slowMo: 500, // 减慢操作速度便于观察
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox'
          ]
        }
      },
    },

    // 可选：Firefox测试
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // 可选：Safari测试
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    // 移动端测试
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
  ],

  // Web服务器配置 - 暂时禁用，使用外部服务器
  // webServer: {
  //   command: 'npm run dev',
  //   url: 'http://localhost:3000',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120000, // 2分钟启动超时
  //   stdout: 'pipe',
  //   stderr: 'pipe',
  // },
});
