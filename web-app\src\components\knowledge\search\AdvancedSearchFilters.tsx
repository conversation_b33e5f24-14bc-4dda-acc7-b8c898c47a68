'use client';

// MySQLAi.de - 高级搜索筛选组件
// 提供分类、难度、标签、排序等多维度搜索筛选功能

import React, { useState, useEffect, useCallback } from 'react';
import {
  Filter,
  X,
  ChevronDown,
  Tag,
  Settings,
  Calendar,
  Star
} from 'lucide-react';

// 筛选选项类型
interface FilterOption {
  value: string;
  label: string;
  count?: number;
  color?: string;
  icon?: React.ReactNode;
}

// 筛选状态类型
interface FilterState {
  category?: string;
  difficulty?: string;
  tags: string[];
  sortBy: 'relevance' | 'date' | 'title';
  sortOrder: 'asc' | 'desc';
  dateRange?: {
    start?: string;
    end?: string;
  };
}

// 组件属性
interface AdvancedSearchFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  categories?: FilterOption[];
  availableTags?: FilterOption[];
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  showResultCount?: boolean;
  resultCount?: number;
}

// 难度选项
const DIFFICULTY_OPTIONS: FilterOption[] = [
  { value: 'beginner', label: '初级', color: 'bg-green-100 text-green-800' },
  { value: 'intermediate', label: '中级', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'advanced', label: '高级', color: 'bg-red-100 text-red-800' },
  { value: 'expert', label: '专家', color: 'bg-purple-100 text-purple-800' }
];

// 排序选项
const SORT_OPTIONS: FilterOption[] = [
  { value: 'relevance', label: '相关性', icon: <Star className="w-4 h-4" /> },
  { value: 'date', label: '更新时间', icon: <Calendar className="w-4 h-4" /> },
  { value: 'title', label: '标题', icon: <Tag className="w-4 h-4" /> }
];

export default function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  categories = [],
  availableTags = [],
  className = '',
  isCollapsed = false,
  onToggleCollapse,
  showResultCount = true,
  resultCount = 0
}: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(!isCollapsed);
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const [showTagDropdown, setShowTagDropdown] = useState(false);

  // 处理筛选器变化
  const handleFilterChange = useCallback((key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange(newFilters);
  }, [filters, onFiltersChange]);

  // 处理标签添加
  const handleTagAdd = useCallback((tagValue: string) => {
    if (!filters.tags.includes(tagValue)) {
      const newTags = [...filters.tags, tagValue];
      handleFilterChange('tags', newTags);
    }
    setTagSearchQuery('');
    setShowTagDropdown(false);
  }, [filters.tags, handleFilterChange]);

  // 处理标签移除
  const handleTagRemove = useCallback((tagValue: string) => {
    const newTags = filters.tags.filter(tag => tag !== tagValue);
    handleFilterChange('tags', newTags);
  }, [filters.tags, handleFilterChange]);

  // 清空所有筛选器
  const handleClearAll = useCallback(() => {
    onFiltersChange({
      tags: [],
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  }, [onFiltersChange]);

  // 切换展开/收起
  const handleToggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onToggleCollapse?.();
  };

  // 过滤可用标签
  const filteredTags = availableTags.filter(tag => 
    tag.label.toLowerCase().includes(tagSearchQuery.toLowerCase()) &&
    !filters.tags.includes(tag.value)
  );

  // 检查是否有活动筛选器
  const hasActiveFilters = filters.category || 
                          filters.difficulty || 
                          filters.tags.length > 0 ||
                          filters.sortBy !== 'relevance' ||
                          filters.sortOrder !== 'desc';

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <button
            type="button"
            onClick={handleToggleExpanded}
            className="flex items-center gap-2 text-gray-700 hover:text-gray-900 transition-colors"
          >
            <Filter className="w-5 h-5" />
            <span className="font-medium">高级筛选</span>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              }`}
            />
          </button>
          
          {hasActiveFilters && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {[
                filters.category ? 1 : 0,
                filters.difficulty ? 1 : 0,
                filters.tags.length,
                (filters.sortBy !== 'relevance' || filters.sortOrder !== 'desc') ? 1 : 0
              ].reduce((a, b) => a + b, 0)} 个筛选器
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {showResultCount && (
            <span className="text-sm text-gray-500">
              {resultCount} 个结果
            </span>
          )}
          
          {hasActiveFilters && (
            <button
              type="button"
              onClick={handleClearAll}
              className="text-sm text-gray-500 hover:text-red-600 transition-colors"
            >
              清空筛选
            </button>
          )}
        </div>
      </div>

      {/* 筛选器内容 */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {/* 分类筛选 */}
          {categories.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                分类
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                <button
                  type="button"
                  onClick={() => handleFilterChange('category', undefined)}
                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                    !filters.category
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  全部分类
                </button>
                {categories.map((category) => (
                  <button
                    key={category.value}
                    type="button"
                    onClick={() => handleFilterChange('category', category.value)}
                    className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                      filters.category === category.value
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {category.label}
                    {category.count && (
                      <span className="ml-1 text-xs text-gray-500">
                        ({category.count})
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 难度筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              难度等级
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleFilterChange('difficulty', undefined)}
                className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                  !filters.difficulty
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                }`}
              >
                全部难度
              </button>
              {DIFFICULTY_OPTIONS.map((difficulty) => (
                <button
                  key={difficulty.value}
                  type="button"
                  onClick={() => handleFilterChange('difficulty', difficulty.value)}
                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                    filters.difficulty === difficulty.value
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${difficulty.color?.split(' ')[0] || 'bg-gray-300'}`}></span>
                  {difficulty.label}
                </button>
              ))}
            </div>
          </div>

          {/* 标签筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              标签
            </label>
            
            {/* 已选标签 */}
            {filters.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {filters.tags.map((tag) => {
                  const tagOption = availableTags.find(t => t.value === tag);
                  return (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                    >
                      <Tag className="w-3 h-3" />
                      {tagOption?.label || tag}
                      <button
                        type="button"
                        onClick={() => handleTagRemove(tag)}
                        className="ml-1 hover:text-blue-600"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  );
                })}
              </div>
            )}

            {/* 标签搜索 */}
            <div className="relative">
              <input
                type="text"
                value={tagSearchQuery}
                onChange={(e) => setTagSearchQuery(e.target.value)}
                onFocus={() => setShowTagDropdown(true)}
                onBlur={() => setTimeout(() => setShowTagDropdown(false), 200)}
                placeholder="搜索标签..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              
              {/* 标签下拉列表 */}
              {showTagDropdown && filteredTags.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                  {filteredTags.slice(0, 10).map((tag) => (
                    <button
                      key={tag.value}
                      type="button"
                      onClick={() => handleTagAdd(tag.value)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Tag className="w-4 h-4 text-gray-400" />
                      <span>{tag.label}</span>
                      {tag.count && (
                        <span className="ml-auto text-xs text-gray-500">
                          {tag.count}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 排序选项 */}
          <div className="flex items-center gap-4 pt-2 border-t border-gray-200">
            <div className="flex items-center gap-2">
              <Settings className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">排序:</span>
            </div>
            
            <div className="flex items-center gap-2">
              {SORT_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handleFilterChange('sortBy', option.value)}
                  className={`flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors ${
                    filters.sortBy === option.value
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {option.icon}
                  {option.label}
                </button>
              ))}
            </div>

            <button
              type="button"
              onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
              className="flex items-center gap-1 px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
              title={filters.sortOrder === 'asc' ? '升序' : '降序'}
            >
              <span className={`transform transition-transform ${filters.sortOrder === 'asc' ? 'rotate-180' : ''}`}>
                ↓
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
