'use client';

// MySQLAi.de - 智能搜索建议组件
// 提供实时搜索建议、键盘导航、搜索历史显示功能

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { searchApi } from '@/lib/api/knowledge';
import { ChevronRight, Clock, TrendingUp, FileText } from 'lucide-react';

// 建议项类型
interface SuggestionItem {
  type: 'article' | 'query' | 'history';
  text: string;
  id?: string;
  category?: string;
  icon?: React.ReactNode;
}

// 组件属性
interface SearchSuggestionsProps {
  query: string;
  isVisible: boolean;
  onSelect: (suggestion: SuggestionItem) => void;
  onClose: () => void;
  searchHistory?: string[];
  className?: string;
  maxSuggestions?: number;
  showHistory?: boolean;
  showCategories?: boolean;
}

export default function SearchSuggestions({
  query,
  isVisible,
  onSelect,
  onClose,
  searchHistory = [],
  className = '',
  maxSuggestions = 8,
  showHistory = true,
  showCategories = true
}: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [error, setError] = useState<string | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // 使用ref存储最新值，避免useEffect依赖问题
  const suggestionsRef = useRef(suggestions);
  const selectedIndexRef = useRef(selectedIndex);

  // 更新ref值
  suggestionsRef.current = suggestions;
  selectedIndexRef.current = selectedIndex;

  // 获取搜索建议
  const fetchSuggestions = useCallback(async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await searchApi.getSuggestions(searchQuery, maxSuggestions - 2);

      if (response.success && response.data) {
        const apiSuggestions: SuggestionItem[] = response.data.map(item => ({
          ...item,
          icon: item.type === 'article' ? <FileText className="w-4 h-4" /> : <TrendingUp className="w-4 h-4" />
        }));

        // 添加搜索历史建议 - 使用当前的props值而不是依赖
        const historySuggestions: SuggestionItem[] = showHistory
          ? searchHistory
              .filter(historyItem =>
                historyItem.toLowerCase().includes(searchQuery.toLowerCase()) &&
                historyItem !== searchQuery
              )
              .slice(0, 2)
              .map(historyItem => ({
                type: 'history' as const,
                text: historyItem,
                icon: <Clock className="w-4 h-4" />
              }))
          : [];

        // 合并建议并去重
        const allSuggestions = [...historySuggestions, ...apiSuggestions];
        const uniqueSuggestions = allSuggestions.filter((suggestion, index, self) =>
          index === self.findIndex(s => s.text === suggestion.text)
        );

        setSuggestions(uniqueSuggestions.slice(0, maxSuggestions));
      } else {
        setError(response.error || '获取建议失败');
        setSuggestions([]);
      }
    } catch (err) {
      console.error('获取搜索建议失败:', err);
      setError('网络错误，请稍后重试');
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  }, [maxSuggestions]); // 只保留稳定的依赖

  // 监听查询变化
  useEffect(() => {
    if (isVisible && query) {
      fetchSuggestions(query);
    } else {
      setSuggestions([]);
      setSelectedIndex(-1);
    }
  }, [query, isVisible]); // 移除fetchSuggestions依赖，避免循环

  // 键盘导航处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isVisible || suggestionsRef.current.length === 0) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev =>
            prev < suggestionsRef.current.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev =>
            prev > 0 ? prev - 1 : suggestionsRef.current.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (selectedIndexRef.current >= 0 && selectedIndexRef.current < suggestionsRef.current.length) {
            onSelect(suggestionsRef.current[selectedIndexRef.current]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isVisible, onSelect, onClose]); // 只依赖isVisible和稳定的回调函数

  // 滚动到选中项
  useEffect(() => {
    if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {
      itemRefs.current[selectedIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [selectedIndex]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isVisible, onClose]);

  // 处理建议项点击
  const handleSuggestionClick = (suggestion: SuggestionItem) => {
    onSelect(suggestion);
  };

  // 获取建议项样式
  const getSuggestionItemClass = (index: number) => {
    const baseClass = "flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150";
    const selectedClass = index === selectedIndex 
      ? "bg-blue-50 text-blue-700 border-l-2 border-blue-500" 
      : "hover:bg-gray-50 text-gray-700";
    
    return `${baseClass} ${selectedClass}`;
  };

  // 获取建议类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'article': return '文章';
      case 'query': return '热门';
      case 'history': return '历史';
      default: return '';
    }
  };

  if (!isVisible) return null;

  return (
    <div 
      ref={containerRef}
      className={`absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto ${className}`}
      role="listbox"
      aria-label="搜索建议"
    >
      {loading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-sm text-gray-500">获取建议中...</span>
        </div>
      )}

      {error && (
        <div className="px-4 py-3 text-sm text-red-600 bg-red-50">
          {error}
        </div>
      )}

      {!loading && !error && suggestions.length === 0 && query.length >= 2 && (
        <div className="px-4 py-3 text-sm text-gray-500 text-center">
          暂无相关建议
        </div>
      )}

      {!loading && !error && suggestions.length > 0 && (
        <div className="py-1">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.type}-${suggestion.text}-${index}`}
              ref={el => itemRefs.current[index] = el}
              className={getSuggestionItemClass(index)}
              onClick={() => handleSuggestionClick(suggestion)}
              role="option"
              aria-selected={index === selectedIndex}
            >
              <div className="flex-shrink-0 text-gray-400">
                {suggestion.icon}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium truncate">
                    {suggestion.text}
                  </span>
                  {showCategories && suggestion.category && (
                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                      {suggestion.category}
                    </span>
                  )}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {getTypeLabel(suggestion.type)}
                </div>
              </div>

              <ChevronRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
            </div>
          ))}
        </div>
      )}

      {!loading && !error && suggestions.length > 0 && (
        <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t">
          使用 ↑↓ 键导航，回车选择，ESC 关闭
        </div>
      )}
    </div>
  );
}
