'use client';

// MySQLAi.de - 知识库基础表单组件
// 管理后台专用的统一表单组件

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AlertCircle, 
  CheckCircle, 
  Eye, 
  EyeOff,
  Upload,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

// 表单字段类型
interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'number' | 'email' | 'password' | 'file' | 'switch' | 'radio' | 'checkbox';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: { label: string; value: string | number }[];
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: any) => string | null;
  };
  description?: string;
  rows?: number; // for textarea
  accept?: string; // for file input
  multiple?: boolean; // for file input and multiselect
}

interface KnowledgeFormProps {
  fields: FormField[];
  values: Record<string, any>;
  errors: Record<string, string>;
  onChange: (name: string, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  loading?: boolean;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
  className?: string;
}

export default function KnowledgeForm({
  fields,
  values,
  errors,
  onChange,
  onSubmit,
  loading = false,
  submitText = '保存',
  cancelText = '取消',
  onCancel,
  className
}: KnowledgeFormProps) {
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});

  // 切换密码显示
  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  // 处理文件上传
  const handleFileChange = (fieldName: string, files: FileList | null) => {
    const field = fields.find(f => f.name === fieldName);
    if (!files) return;

    if (field?.multiple) {
      onChange(fieldName, Array.from(files));
    } else {
      onChange(fieldName, files[0] || null);
    }
  };

  // 渲染表单字段
  const renderField = (field: FormField) => {
    const value = values[field.name];
    const error = errors[field.name];
    const hasError = Boolean(error);

    const baseInputClasses = cn(
      'w-full px-3 py-2 border rounded-lg transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/20',
      hasError 
        ? 'border-red-500 focus:border-red-500' 
        : 'border-mysql-border focus:border-mysql-primary',
      field.disabled && 'bg-gray-50 cursor-not-allowed opacity-60'
    );

    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            id={field.name}
            value={value || ''}
            onChange={(e) => onChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled || loading}
            rows={field.rows || 4}
            aria-describedby={field.description ? `${field.name}-description` : undefined}
            className={baseInputClasses}
          />
        );

      case 'select':
        return (
          <select
            id={field.name}
            value={value || ''}
            onChange={(e) => onChange(field.name, e.target.value)}
            disabled={field.disabled || loading}
            aria-describedby={field.description ? `${field.name}-description` : undefined}
            className={baseInputClasses}
          >
            <option value="">{field.placeholder || '请选择'}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            {field.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={(value || []).includes(option.value)}
                  onChange={(e) => {
                    const currentValues = value || [];
                    const newValues = e.target.checked
                      ? [...currentValues, option.value]
                      : currentValues.filter((v: any) => v !== option.value);
                    onChange(field.name, newValues);
                  }}
                  disabled={field.disabled || loading}
                  className="w-4 h-4 text-mysql-primary border-mysql-border rounded focus:ring-mysql-primary"
                />
                <span className="text-sm text-mysql-text">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={field.name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => onChange(field.name, e.target.value)}
                  disabled={field.disabled || loading}
                  className="w-4 h-4 text-mysql-primary border-mysql-border focus:ring-mysql-primary"
                />
                <span className="text-sm text-mysql-text">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'switch':
        return (
          <label className="flex items-center space-x-3">
            <div className="relative">
              <input
                type="checkbox"
                checked={Boolean(value)}
                onChange={(e) => onChange(field.name, e.target.checked)}
                disabled={field.disabled || loading}
                className="sr-only"
              />
              <div className={cn(
                'w-11 h-6 rounded-full transition-colors duration-200',
                value ? 'bg-mysql-primary' : 'bg-gray-300'
              )}>
                <div className={cn(
                  'w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-200',
                  'absolute top-0.5',
                  value ? 'translate-x-5' : 'translate-x-0.5'
                )} />
              </div>
            </div>
            <span className="text-sm text-mysql-text">{field.label}</span>
          </label>
        );

      case 'password':
        return (
          <div className="relative">
            <input
              id={field.name}
              type={showPasswords[field.name] ? 'text' : 'password'}
              value={value || ''}
              onChange={(e) => onChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              disabled={field.disabled || loading}
              aria-describedby={field.description ? `${field.name}-description` : undefined}
              className={cn(baseInputClasses, 'pr-10')}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility(field.name)}
              aria-label={showPasswords[field.name] ? '隐藏密码' : '显示密码'}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-mysql-text-light hover:text-mysql-primary"
            >
              {showPasswords[field.name] ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
        );

      case 'file':
        return (
          <div className="space-y-2">
            <div className={cn(
              'border-2 border-dashed rounded-lg p-4 text-center transition-colors duration-200',
              hasError ? 'border-red-300' : 'border-mysql-border hover:border-mysql-primary'
            )}>
              <input
                type="file"
                onChange={(e) => handleFileChange(field.name, e.target.files)}
                accept={field.accept}
                multiple={field.multiple}
                disabled={field.disabled || loading}
                className="hidden"
                id={field.name}
              />
              <label htmlFor={field.name} className="cursor-pointer">
                <Upload className="w-8 h-8 mx-auto mb-2 text-mysql-text-light" />
                <p className="text-sm text-mysql-text-light">
                  {field.placeholder || '点击上传文件'}
                </p>
              </label>
            </div>
            {value && (
              <div className="text-sm text-mysql-text">
                {field.multiple 
                  ? `已选择 ${value.length} 个文件`
                  : `已选择: ${value.name}`
                }
              </div>
            )}
          </div>
        );

      default:
        return (
          <input
            id={field.name}
            type={field.type}
            value={value || ''}
            onChange={(e) => onChange(field.name, field.type === 'number' ? Number(e.target.value) : e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled || loading}
            min={field.validation?.min}
            max={field.validation?.max}
            aria-describedby={field.description ? `${field.name}-description` : undefined}
            className={baseInputClasses}
          />
        );
    }
  };

  return (
    <motion.form
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onSubmit={onSubmit}
      className={cn('space-y-6', className)}
    >
      {fields.map((field) => {
        const error = errors[field.name];
        const hasError = Boolean(error);

        return (
          <motion.div
            key={field.name}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="space-y-2"
          >
            {/* 字段标签 */}
            {field.type !== 'switch' && (
              <label htmlFor={field.name} className="block text-sm font-medium text-mysql-text">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
            )}

            {/* 字段输入 */}
            {renderField(field)}

            {/* 字段描述 */}
            {field.description && (
              <p id={`${field.name}-description`} className="text-xs text-mysql-text-light">{field.description}</p>
            )}

            {/* 错误信息 */}
            {hasError && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="flex items-center space-x-1 text-red-600"
              >
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{error}</span>
              </motion.div>
            )}
          </motion.div>
        );
      })}

      {/* 表单操作按钮 */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-mysql-border">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
        >
          {submitText}
        </Button>
      </div>
    </motion.form>
  );
}
