'use client';

// MySQLAi.de - 路由重定向组件
// 处理旧路由到新知识库模块路由的重定向

import React, { useEffect } from 'react';
import { useRouter, usePathname, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { ArrowRight, Info } from 'lucide-react';
import { getRedirectRoute } from '@/lib/knowledge-routes';

interface RouteRedirectProps {
  // 显示重定向提示
  showNotice?: boolean;
  // 自动重定向延迟（毫秒）
  redirectDelay?: number;
  // 自定义重定向消息
  message?: string;
}

export default function RouteRedirect({
  showNotice = true,
  redirectDelay = 3000,
  message
}: RouteRedirectProps) {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  useEffect(() => {
    // 获取重定向路由
    const redirectPath = getRedirectRoute(pathname, params as Record<string, string>);
    
    if (redirectPath) {
      // 延迟重定向，给用户时间看到提示
      const timer = setTimeout(() => {
        router.replace(redirectPath);
      }, showNotice ? redirectDelay : 0);

      return () => clearTimeout(timer);
    }
  }, [pathname, params, router, redirectDelay, showNotice]);

  // 如果不显示提示，直接返回null
  if (!showNotice) {
    return null;
  }

  // 获取重定向目标路径
  const redirectPath = getRedirectRoute(pathname, params as Record<string, string>);
  
  if (!redirectPath) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen flex items-center justify-center bg-gray-50"
    >
      <div className="max-w-md w-full mx-4">
        <motion.div
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg border border-mysql-border p-8 text-center"
        >
          {/* 图标 */}
          <motion.div
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="flex items-center justify-center w-16 h-16 bg-mysql-primary-light rounded-full mx-auto mb-6"
          >
            <ArrowRight className="w-8 h-8 text-mysql-primary" />
          </motion.div>

          {/* 标题 */}
          <h1 className="text-2xl font-bold text-mysql-text mb-4">
            页面已迁移
          </h1>

          {/* 消息 */}
          <p className="text-mysql-text-light mb-6 leading-relaxed">
            {message || '此页面已迁移到新的知识库管理模块。您将在几秒钟后自动跳转到新页面。'}
          </p>

          {/* 路径信息 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center space-x-2 text-sm">
              <span className="text-mysql-text-light">从:</span>
              <code className="bg-white px-2 py-1 rounded text-mysql-text font-mono">
                {pathname}
              </code>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm mt-2">
              <span className="text-mysql-text-light">到:</span>
              <code className="bg-mysql-primary-light px-2 py-1 rounded text-mysql-primary font-mono">
                {redirectPath}
              </code>
            </div>
          </div>

          {/* 提示信息 */}
          <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-left">
              <h3 className="text-sm font-medium text-blue-800 mb-1">
                重要提示
              </h3>
              <p className="text-sm text-blue-700">
                请更新您的书签和链接到新的地址。旧的路径将在未来版本中移除。
              </p>
            </div>
          </div>

          {/* 手动跳转按钮 */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => router.replace(redirectPath)}
            className="mt-6 w-full bg-mysql-primary text-white py-3 px-6 rounded-lg font-medium hover:bg-mysql-primary-dark transition-colors duration-200"
          >
            立即跳转
          </motion.button>

          {/* 倒计时 */}
          <CountdownTimer 
            duration={redirectDelay} 
            onComplete={() => router.replace(redirectPath)}
          />
        </motion.div>
      </div>
    </motion.div>
  );
}

// 倒计时组件
interface CountdownTimerProps {
  duration: number;
  onComplete: () => void;
}

function CountdownTimer({ duration, onComplete }: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = React.useState(Math.ceil(duration / 1000));

  useEffect(() => {
    if (timeLeft <= 0) {
      onComplete();
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft, onComplete]);

  return (
    <motion.p
      key={timeLeft}
      initial={{ opacity: 0.5 }}
      animate={{ opacity: 1 }}
      className="text-sm text-mysql-text-light mt-4"
    >
      {timeLeft > 0 ? `${timeLeft} 秒后自动跳转...` : '正在跳转...'}
    </motion.p>
  );
}
