// MySQLAi.de - TypeScript类型定义文件
// 为整个项目提供类型安全保障

import { ReactNode } from 'react';

// 基础组件Props接口
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
}

// 导航相关类型
export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
  isExternal?: boolean;
  children?: NavigationItem[]; // 支持下拉子菜单
}

export interface NavigationProps extends BaseComponentProps {
  items: NavigationItem[];
  logo?: string;
  onMenuToggle?: () => void;
  isMenuOpen?: boolean;
}

// 工具相关类型
export interface ToolItem {
  id: string;
  name: string;
  description: string;
  href: string;
  icon: string;
  category?: string;
}

// 下拉菜单组件类型
export interface DropdownMenuProps extends BaseComponentProps {
  trigger: ReactNode;
  items: NavigationItem[];
  align?: 'left' | 'right' | 'center';
  onItemClick?: (item: NavigationItem) => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// Hero区域类型
export interface HeroProps extends BaseComponentProps {
  title: string;
  subtitle: string;
  description: string;
  primaryButton: ButtonProps;
  secondaryButton?: ButtonProps;
  backgroundImage?: string;
}

// 按钮组件类型
export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  type?: 'button' | 'submit' | 'reset';
}

// 功能卡片类型
export interface FeatureCardProps extends BaseComponentProps {
  title: string;
  description: string;
  icon: ReactNode;
  href?: string;
  features?: string[];
}

export interface FeaturesProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  features: FeatureCardProps[];
}

// 关于我们/专业特性类型
export interface AboutFeatureProps {
  title: string;
  description: string;
  icon: ReactNode;
}

export interface AboutProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  features: AboutFeatureProps[];
}

// 优势展示类型
export interface AdvantageProps {
  title: string;
  description: string;
  details: string;
  icon: string; // emoji或图标字符串
}

export interface AdvantagesProps extends BaseComponentProps {
  title: string;
  advantages: AdvantageProps[];
}

// 联系支持类型
export interface ContactProps extends BaseComponentProps {
  title: string;
  subtitle: string;
  supportHours: string;
  contactMethods: ContactMethod[];
}

export interface ContactMethod {
  type: 'email' | 'phone' | 'chat' | 'form';
  label: string;
  value: string;
  icon: ReactNode;
}

// 页脚类型
export interface FooterLink {
  name: string;
  href: string;
  isExternal?: boolean;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}

export interface FooterProps extends BaseComponentProps {
  logo?: string;
  sections: FooterSection[];
  socialLinks?: FooterLink[];
  copyright: string;
  legalLinks?: FooterLink[];
}

// 动画相关类型
export interface AnimationProps {
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';
  once?: boolean;
}

// 响应式断点类型
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// 主题色彩类型
export interface ThemeColors {
  primary: string;
  primaryDark: string;
  primaryLight: string;
  accent: string;
  text: string;
  textLight: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

// 页面元数据类型
export interface PageMetadata {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonical?: string;
}

// API响应类型（为将来的API集成预留）
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 表单相关类型
export interface FormFieldProps {
  name: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  placeholder?: string;
  required?: boolean;
  options?: { label: string; value: string }[];
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    custom?: (value: string) => boolean | string;
  };
}

export interface FormProps extends BaseComponentProps {
  fields: FormFieldProps[];
  onSubmit: (data: Record<string, string>) => void;
  submitLabel?: string;
  loading?: boolean;
}

// 法律页面相关类型
export interface LegalPageProps extends BaseComponentProps {
  type: 'terms' | 'privacy' | 'disclaimer' | 'cookies';
  title: string;
  lastUpdated: string;
}

export interface BreadcrumbProps extends BaseComponentProps {
  pathname: string;
  maxItems?: number;
}

export interface LegalNavigationProps extends BaseComponentProps {
  currentType?: 'terms' | 'privacy' | 'disclaimer' | 'cookies';
  links: Array<{
    type: string;
    title: string;
    href: string;
    description: string;
  }>;
}

// 知识库相关类型 - 严格按照数据库结构定义
export interface KnowledgeCategory {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  order_index: number;
  created_at: string;
}

export interface CodeExample {
  id: string;
  article_id: string;
  title: string;
  code: string;
  language: string;
  description: string | null;
  order_index: number;
  created_at: string;
}

export interface KnowledgeArticle {
  id: string;
  title: string;
  description: string | null;
  content: string;
  category_id: string | null;
  tags: string[] | null;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  order_index: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
}

// 兼容性别名，保持向后兼容
export type KnowledgeItem = KnowledgeArticle;

export interface KnowledgeCardProps extends BaseComponentProps {
  item: KnowledgeItem;
  displayMode?: 'list' | 'grid';
  onClick?: () => void;
}



export interface CodeBlockProps extends BaseComponentProps {
  code: string;
  language: string;
  title?: string;
  showLineNumbers?: boolean;
  copyable?: boolean;
}

export interface RelatedItemsProps extends BaseComponentProps {
  currentItem: KnowledgeItem;
  maxItems?: number;
}

export interface KnowledgePageProps extends BaseComponentProps {
  categories: KnowledgeCategory[];
  featuredItems?: KnowledgeItem[];
  recentItems?: KnowledgeItem[];
}

export interface CategoryPageProps extends BaseComponentProps {
  category: KnowledgeCategory;
  items: KnowledgeItem[];
  sortBy?: 'order' | 'title' | 'lastUpdated';
  filterBy?: {
    difficulty?: KnowledgeItem['difficulty'];
    tags?: string[];
  };
}

export interface ItemPageProps extends BaseComponentProps {
  item: KnowledgeItem;
  category: KnowledgeCategory;
  relatedItems?: KnowledgeItem[];
  navigation?: {
    prev?: KnowledgeItem;
    next?: KnowledgeItem;
  };
}

// 搜索历史相关类型
export interface SearchHistoryItem {
  id: string;
  query: string;
  results_count: number;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface SearchHistoryProps extends BaseComponentProps {
  items: SearchHistoryItem[];
  onSelectQuery?: (query: string) => void;
  onClearHistory?: () => void;
  onDeleteItem?: (id: string) => void;
  maxItems?: number;
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;
