'use client';

// MySQLAi.de - 知识库搜索栏组件
// 管理后台专用的统一搜索组件

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, X, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface KnowledgeSearchBarProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (query: string) => void;
  onClear?: () => void;
  showFilter?: boolean;
  onFilterClick?: () => void;
  loading?: boolean;
  className?: string;
}

export default function KnowledgeSearchBar({
  placeholder = '搜索内容...',
  value = '',
  onChange,
  onSearch,
  onClear,
  showFilter = true,
  onFilterClick,
  loading = false,
  className
}: KnowledgeSearchBarProps) {
  const [isFocused, setIsFocused] = useState(false);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange?.(newValue);
  };

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(value);
  };

  // 处理清空
  const handleClear = () => {
    onChange?.('');
    onClear?.();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      handleClear();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex items-center space-x-3',
        className
      )}
    >
      {/* 搜索输入框 */}
      <div className={cn(
        'relative flex-1 transition-all duration-200',
        isFocused && 'transform scale-[1.02]'
      )}>
        <div className="relative">
          {/* 搜索图标 */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <Search className={cn(
              'w-5 h-5 transition-colors duration-200',
              isFocused ? 'text-mysql-primary' : 'text-mysql-text-light'
            )} />
          </div>

          {/* 输入框 */}
          <input
            type="text"
            value={value}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            className={cn(
              'w-full pl-10 pr-12 py-3 text-base',
              'bg-white border-2 rounded-lg',
              'focus:outline-none focus:ring-4 focus:ring-mysql-primary/20',
              'placeholder-mysql-text-light text-mysql-text',
              'transition-all duration-200',
              isFocused 
                ? 'border-mysql-primary shadow-lg' 
                : 'border-mysql-border hover:border-mysql-primary/50'
            )}
            disabled={loading}
          />

          {/* 清空按钮 */}
          {value && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              onClick={handleClear}
              className={cn(
                'absolute right-3 top-1/2 transform -translate-y-1/2',
                'p-1 rounded-full transition-colors duration-200',
                'text-mysql-text-light hover:text-mysql-primary hover:bg-mysql-primary-light'
              )}
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}
        </div>

        {/* 加载指示器 */}
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-5 h-5 border-2 border-mysql-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* 搜索按钮 */}
      <Button
        variant="primary"
        onClick={handleSearch}
        disabled={loading || !value.trim()}
        loading={loading}
        icon={<Search className="w-4 h-4" />}
        className="px-6"
      >
        搜索
      </Button>

      {/* 筛选按钮 */}
      {showFilter && (
        <Button
          variant="outline"
          onClick={onFilterClick}
          disabled={loading}
          icon={<Filter className="w-4 h-4" />}
          className="px-4"
        >
          筛选
        </Button>
      )}
    </motion.div>
  );
}
