'use client';

// MySQLAi.de - 知识库筛选组件
// 管理后台专用的统一筛选组件

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Filter, 
  X, 
  ChevronDown, 
  Calendar,
  Tag,
  FolderOpen,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

// 筛选选项类型
interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

// 筛选配置类型
interface FilterConfig {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  options: FilterOption[];
  multiple?: boolean;
}

interface KnowledgeFilterProps {
  filters: FilterConfig[];
  values: Record<string, string | string[]>;
  onChange: (key: string, value: string | string[]) => void;
  onReset: () => void;
  onApply?: () => void;
  isOpen?: boolean;
  onToggle?: () => void;
  className?: string;
}

export default function KnowledgeFilter({
  filters,
  values,
  onChange,
  onReset,
  onApply,
  isOpen = false,
  onToggle,
  className
}: KnowledgeFilterProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  // 切换展开状态
  const toggleSection = (key: string) => {
    setExpandedSections(prev => 
      prev.includes(key) 
        ? prev.filter(k => k !== key)
        : [...prev, key]
    );
  };

  // 处理单选筛选
  const handleSingleSelect = (filterKey: string, value: string) => {
    const currentValue = values[filterKey] as string;
    onChange(filterKey, currentValue === value ? '' : value);
  };

  // 处理多选筛选
  const handleMultiSelect = (filterKey: string, value: string) => {
    const currentValues = (values[filterKey] as string[]) || [];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    onChange(filterKey, newValues);
  };

  // 获取激活的筛选数量
  const getActiveFiltersCount = () => {
    return Object.values(values).reduce((count, value) => {
      if (Array.isArray(value)) {
        return count + value.length;
      }
      return count + (value ? 1 : 0);
    }, 0);
  };

  const activeCount = getActiveFiltersCount();

  return (
    <div className={cn('relative', className)}>
      {/* 筛选触发按钮 */}
      <Button
        variant={isOpen ? "primary" : "outline"}
        onClick={onToggle}
        icon={<Filter className="w-4 h-4" />}
        className={cn(
          'relative',
          activeCount > 0 && 'ring-2 ring-mysql-primary/30'
        )}
      >
        筛选
        {activeCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-mysql-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {activeCount}
          </span>
        )}
      </Button>

      {/* 筛选面板 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              'absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-mysql-border z-50',
              'max-h-96 overflow-y-auto'
            )}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-4 border-b border-mysql-border">
              <h3 className="text-lg font-semibold text-mysql-text">
                筛选条件
              </h3>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onReset}
                  icon={<RefreshCw className="w-4 h-4" />}
                  disabled={activeCount === 0}
                >
                  重置
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggle}
                  icon={<X className="w-4 h-4" />}
                >
                </Button>
              </div>
            </div>

            {/* 筛选内容 */}
            <div className="p-4 space-y-4">
              {filters.map((filter) => {
                const isExpanded = expandedSections.includes(filter.key);
                const IconComponent = filter.icon;
                const currentValue = values[filter.key];
                const hasValue = filter.multiple 
                  ? Array.isArray(currentValue) && currentValue.length > 0
                  : Boolean(currentValue);

                return (
                  <div key={filter.key} className="space-y-2">
                    {/* 筛选分组标题 */}
                    <button
                      onClick={() => toggleSection(filter.key)}
                      className={cn(
                        'w-full flex items-center justify-between p-2 rounded-lg transition-colors duration-200',
                        'hover:bg-mysql-primary-light',
                        hasValue && 'bg-mysql-primary-light text-mysql-primary'
                      )}
                    >
                      <div className="flex items-center space-x-2">
                        <IconComponent className="w-4 h-4" />
                        <span className="font-medium">{filter.label}</span>
                        {hasValue && (
                          <span className="text-xs bg-mysql-primary text-white px-2 py-1 rounded-full">
                            {filter.multiple 
                              ? (currentValue as string[]).length
                              : 1
                            }
                          </span>
                        )}
                      </div>
                      <ChevronDown className={cn(
                        'w-4 h-4 transition-transform duration-200',
                        isExpanded && 'rotate-180'
                      )} />
                    </button>

                    {/* 筛选选项 */}
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="pl-6 space-y-1">
                            {filter.options.map((option) => {
                              const isSelected = filter.multiple
                                ? (currentValue as string[] || []).includes(option.value)
                                : currentValue === option.value;

                              return (
                                <label
                                  key={option.value}
                                  className={cn(
                                    'flex items-center justify-between p-2 rounded cursor-pointer transition-colors duration-200',
                                    'hover:bg-gray-50',
                                    isSelected && 'bg-mysql-primary-light text-mysql-primary'
                                  )}
                                >
                                  <div className="flex items-center space-x-2">
                                    <input
                                      type={filter.multiple ? "checkbox" : "radio"}
                                      name={filter.key}
                                      checked={isSelected}
                                      onChange={() => {
                                        if (filter.multiple) {
                                          handleMultiSelect(filter.key, option.value);
                                        } else {
                                          handleSingleSelect(filter.key, option.value);
                                        }
                                      }}
                                      className="w-4 h-4 text-mysql-primary border-mysql-border rounded focus:ring-mysql-primary"
                                    />
                                    <span className="text-sm">{option.label}</span>
                                  </div>
                                  {option.count !== undefined && (
                                    <span className="text-xs text-mysql-text-light">
                                      {option.count}
                                    </span>
                                  )}
                                </label>
                              );
                            })}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                );
              })}
            </div>

            {/* 底部操作 */}
            {onApply && (
              <div className="p-4 border-t border-mysql-border">
                <Button
                  variant="primary"
                  onClick={onApply}
                  className="w-full"
                >
                  应用筛选
                </Button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
