{"config": {"configFile": "D:\\MysqlAi.De\\web-app\\playwright.config.ts", "rootDir": "D:/MysqlAi.De/web-app/src/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/MysqlAi.De/web-app/test-results/artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "D:/MysqlAi.De/web-app/src/tests", "testIgnore": [], "testMatch": ["**/*playwright*.spec.ts"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 1, "webServer": null}, "suites": [{"title": "knowledge\\component-imports-playwright.spec.ts", "file": "knowledge/component-imports-playwright.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "组件模块导入验证", "file": "knowledge/component-imports-playwright.spec.ts", "line": 8, "column": 6, "specs": [{"title": "搜索模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 3340, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:31:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// 访问包含搜索组件的页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// 等待组件加载\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// 访问包含搜索组件的页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// 等待组件加载\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:13:53.600Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}}, {"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 3395, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:31:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// 访问包含搜索组件的页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// 等待组件加载\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// 访问包含搜索组件的页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// 等待组件加载\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:13:58.702Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-搜索模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-99aae10760c16951723a", "file": "knowledge/component-imports-playwright.spec.ts", "line": 14, "column": 7}, {"title": "导航模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 3452, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:66:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 68 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 69 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 64 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 68 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 69 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:66:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:03.477Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}}, {"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 3383, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:66:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 68 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 69 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 64 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 65 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 68 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 69 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:66:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:08.315Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-导航模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 66}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-f20a089fad674de9cbe7", "file": "knowledge/component-imports-playwright.spec.ts", "line": 53, "column": 7}, {"title": "展示模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 3358, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:99:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}, "snippet": "\u001b[0m \u001b[90m  97 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m  98 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 102 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  97 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m  98 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 102 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:99:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:13.004Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}}, {"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 3356, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:99:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}, "snippet": "\u001b[0m \u001b[90m  97 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m  98 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 102 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  97 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m  98 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 102 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:99:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:17.724Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-展示模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 99}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-cd6461c3833ec11a3595", "file": "knowledge/component-imports-playwright.spec.ts", "line": 86, "column": 7}, {"title": "页面模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 3328, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:133:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}, "snippet": "\u001b[0m \u001b[90m 131 |\u001b[39m     \n \u001b[90m 132 |\u001b[39m     \u001b[90m// 测试知识库主页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 133 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 134 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 136 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 131 |\u001b[39m     \n \u001b[90m 132 |\u001b[39m     \u001b[90m// 测试知识库主页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 133 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 134 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 136 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:133:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:22.440Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}}, {"workerIndex": 7, "parallelIndex": 0, "status": "failed", "duration": 3342, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:133:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}, "snippet": "\u001b[0m \u001b[90m 131 |\u001b[39m     \n \u001b[90m 132 |\u001b[39m     \u001b[90m// 测试知识库主页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 133 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 134 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 136 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 131 |\u001b[39m     \n \u001b[90m 132 |\u001b[39m     \u001b[90m// 测试知识库主页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 133 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 134 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 135 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 136 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:133:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:27.182Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-页面模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 133}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-5ce02d6a8278a4a03c1c", "file": "knowledge/component-imports-playwright.spec.ts", "line": 119, "column": 7}, {"title": "布局模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 3305, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:166:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}, "snippet": "\u001b[0m \u001b[90m 164 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 165 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 164 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 165 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:166:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:31.865Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}}, {"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 3343, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:166:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}, "snippet": "\u001b[0m \u001b[90m 164 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 165 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 164 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 165 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:166:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:36.575Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-布局模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 166}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-c0a6b704226f39aa32b6", "file": "knowledge/component-imports-playwright.spec.ts", "line": 153, "column": 7}, {"title": "管理后台模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 3327, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:199:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}, "snippet": "\u001b[0m \u001b[90m 197 |\u001b[39m     \n \u001b[90m 198 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 199 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 200 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 197 |\u001b[39m     \n \u001b[90m 198 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 199 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 200 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:199:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:41.317Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}}, {"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 3334, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:199:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}, "snippet": "\u001b[0m \u001b[90m 197 |\u001b[39m     \n \u001b[90m 198 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 199 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 200 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 197 |\u001b[39m     \n \u001b[90m 198 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 199 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 200 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 201 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:199:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:46.015Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-管理后台模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 199}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-a52f93242a436bdbadf7", "file": "knowledge/component-imports-playwright.spec.ts", "line": 185, "column": 7}, {"title": "工具模块组件导入测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "failed", "duration": 3430, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:233:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}, "snippet": "\u001b[0m \u001b[90m 231 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 232 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 233 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 231 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 232 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 233 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:233:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:14:50.679Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}}, {"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 3283, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:233:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}, "snippet": "\u001b[0m \u001b[90m 231 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 232 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 233 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 231 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 232 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 233 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts:233:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:14:55.513Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-component-imports-playwright-组件模块导入验证-工具模块组件导入测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\component-imports-playwright.spec.ts", "column": 16, "line": 233}}], "status": "unexpected"}], "id": "b3abef4b5d870e81b81e-fdd1d294770fed13448f", "file": "knowledge/component-imports-playwright.spec.ts", "line": 220, "column": 7}, {"title": "整体模块导入完整性测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "passed", "duration": 11894, "errors": [], "stdout": [{"text": "测试页面: /knowledge\n"}, {"text": "⚠️ 页面 /knowledge 可能需要权限或不存在\n"}, {"text": "测试页面: /admin/knowledge\n"}, {"text": "⚠️ 页面 /admin/knowledge 可能需要权限或不存在\n"}, {"text": "测试页面: /admin/knowledge/articles\n"}, {"text": "⚠️ 页面 /admin/knowledge/articles 可能需要权限或不存在\n"}, {"text": "测试页面: /admin/knowledge/categories\n"}, {"text": "⚠️ 页面 /admin/knowledge/categories 可能需要权限或不存在\n"}, {"text": "✅ 整体模块导入完整性测试通过\n"}, {"text": "📊 测试统计: 0 个错误, 0 个警告\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:00.249Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b3abef4b5d870e81b81e-a978a1d4b3346a349d03", "file": "knowledge/component-imports-playwright.spec.ts", "line": 253, "column": 7}]}]}, {"title": "knowledge\\knowledge-refactor-playwright.spec.ts", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "知识库组件重构验证", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 8, "column": 6, "specs": [{"title": "组件模块导入静态验证测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "passed", "duration": 375, "errors": [], "stdout": [{"text": "✅ 组件模块导入静态验证测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:12.413Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e26cd16a1c4c33c08959-cc038851d3c47f6e66a4", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 26, "column": 7}, {"title": "文件结构验证测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "passed", "duration": 414, "errors": [], "stdout": [{"text": "✅ 文件结构验证测试完成\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:12.794Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e26cd16a1c4c33c08959-1f47ad790bad3d0a3786", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 91, "column": 7}, {"title": "导航功能测试 (navigation 模块)", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "failed", "duration": 3246, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:215:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}, "snippet": "\u001b[0m \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m   test(\u001b[32m'导航功能测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 215 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 217 |\u001b[39m     \n \u001b[90m 218 |\u001b[39m     \u001b[90m// 查找分类导航链接\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m   test(\u001b[32m'导航功能测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 215 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 217 |\u001b[39m     \n \u001b[90m 218 |\u001b[39m     \u001b[90m// 查找分类导航链接\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:215:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:13.217Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}}, {"workerIndex": 15, "parallelIndex": 0, "status": "failed", "duration": 3322, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:215:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}, "snippet": "\u001b[0m \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m   test(\u001b[32m'导航功能测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 215 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 217 |\u001b[39m     \n \u001b[90m 218 |\u001b[39m     \u001b[90m// 查找分类导航链接\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 213 |\u001b[39m\n \u001b[90m 214 |\u001b[39m   test(\u001b[32m'导航功能测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 215 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 216 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 217 |\u001b[39m     \n \u001b[90m 218 |\u001b[39m     \u001b[90m// 查找分类导航链接\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:215:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:15:17.587Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-da460-件重构验证-导航功能测试-navigation-模块--chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 215}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-79220a5341bbbc7b9b6f", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 214, "column": 7}, {"title": "面包屑导航测试 (navigation 模块)", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 16, "parallelIndex": 0, "status": "failed", "duration": 3307, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:245:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}, "snippet": "\u001b[0m \u001b[90m 243 |\u001b[39m   test(\u001b[32m'面包屑导航测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 244 |\u001b[39m     \u001b[90m// 访问一个具体的知识点页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 247 |\u001b[39m     \n \u001b[90m 248 |\u001b[39m     \u001b[90m// 查找面包屑导航\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 243 |\u001b[39m   test(\u001b[32m'面包屑导航测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 244 |\u001b[39m     \u001b[90m// 访问一个具体的知识点页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 247 |\u001b[39m     \n \u001b[90m 248 |\u001b[39m     \u001b[90m// 查找面包屑导航\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:245:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:22.265Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}}, {"workerIndex": 17, "parallelIndex": 0, "status": "failed", "duration": 3271, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:245:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}, "snippet": "\u001b[0m \u001b[90m 243 |\u001b[39m   test(\u001b[32m'面包屑导航测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 244 |\u001b[39m     \u001b[90m// 访问一个具体的知识点页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 247 |\u001b[39m     \n \u001b[90m 248 |\u001b[39m     \u001b[90m// 查找面包屑导航\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 243 |\u001b[39m   test(\u001b[32m'面包屑导航测试 (navigation 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 244 |\u001b[39m     \u001b[90m// 访问一个具体的知识点页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 245 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 246 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 247 |\u001b[39m     \n \u001b[90m 248 |\u001b[39m     \u001b[90m// 查找面包屑导航\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:245:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:15:26.964Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-11868-重构验证-面包屑导航测试-navigation-模块--chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 245}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-171393c999f7e9b9f82c", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 243, "column": 7}, {"title": "页面布局测试 (layout 模块)", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 3273, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:261:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}, "snippet": "\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m   test(\u001b[32m'页面布局测试 (layout 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m     \n \u001b[90m 264 |\u001b[39m     \u001b[90m// 验证页面布局结构\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m   test(\u001b[32m'页面布局测试 (layout 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m     \n \u001b[90m 264 |\u001b[39m     \u001b[90m// 验证页面布局结构\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:261:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:31.651Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}}, {"workerIndex": 19, "parallelIndex": 0, "status": "failed", "duration": 3454, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:261:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}, "snippet": "\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m   test(\u001b[32m'页面布局测试 (layout 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m     \n \u001b[90m 264 |\u001b[39m     \u001b[90m// 验证页面布局结构\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m   test(\u001b[32m'页面布局测试 (layout 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m     \n \u001b[90m 264 |\u001b[39m     \u001b[90m// 验证页面布局结构\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:261:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:15:36.376Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-7f203-知识库组件重构验证-页面布局测试-layout-模块--chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 261}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-db5fcda6d6eb9a76d8d4", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 260, "column": 7}, {"title": "组件导入验证测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 0, "status": "failed", "duration": 3264, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:296:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}, "snippet": "\u001b[0m \u001b[90m 294 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 295 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 297 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 298 |\u001b[39m     \n \u001b[90m 299 |\u001b[39m     \u001b[90m// 等待一段时间让所有组件加载\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 294 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 295 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 297 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 298 |\u001b[39m     \n \u001b[90m 299 |\u001b[39m     \u001b[90m// 等待一段时间让所有组件加载\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:296:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:41.274Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}}, {"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 3240, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:296:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}, "snippet": "\u001b[0m \u001b[90m 294 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 295 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 297 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 298 |\u001b[39m     \n \u001b[90m 299 |\u001b[39m     \u001b[90m// 等待一段时间让所有组件加载\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 294 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 295 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 297 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 298 |\u001b[39m     \n \u001b[90m 299 |\u001b[39m     \u001b[90m// 等待一段时间让所有组件加载\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:296:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:15:45.898Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-组件导入验证测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 296}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-c3e1da1b505a5283e7af", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 282, "column": 7}, {"title": "管理后台组件访问测试 (admin 模块)", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 0, "status": "failed", "duration": 3346, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:321:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}, "snippet": "\u001b[0m \u001b[90m 319 |\u001b[39m   test(\u001b[32m'管理后台组件访问测试 (admin 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 320 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 321 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 322 |\u001b[39m     \n \u001b[90m 323 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n \u001b[90m 324 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 319 |\u001b[39m   test(\u001b[32m'管理后台组件访问测试 (admin 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 320 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 321 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 322 |\u001b[39m     \n \u001b[90m 323 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n \u001b[90m 324 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:321:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:15:50.489Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}}, {"workerIndex": 23, "parallelIndex": 0, "status": "failed", "duration": 3470, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:321:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}, "snippet": "\u001b[0m \u001b[90m 319 |\u001b[39m   test(\u001b[32m'管理后台组件访问测试 (admin 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 320 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 321 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 322 |\u001b[39m     \n \u001b[90m 323 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n \u001b[90m 324 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/admin/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/admin/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 319 |\u001b[39m   test(\u001b[32m'管理后台组件访问测试 (admin 模块)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 320 |\u001b[39m     \u001b[90m// 尝试访问管理后台页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 321 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 322 |\u001b[39m     \n \u001b[90m 323 |\u001b[39m     \u001b[90m// 等待页面加载\u001b[39m\n \u001b[90m 324 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:321:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:15:55.252Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refact-42747-组件重构验证-管理后台组件访问测试-admin-模块--chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 321}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-718df99e9dec33f16024", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 319, "column": 7}, {"title": "性能和加载时间测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "failed", "duration": 3321, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:356:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}, "snippet": "\u001b[0m \u001b[90m 354 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 355 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 356 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 357 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 358 |\u001b[39m     \n \u001b[90m 359 |\u001b[39m     \u001b[36mconst\u001b[39m loadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 354 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 355 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 356 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 357 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 358 |\u001b[39m     \n \u001b[90m 359 |\u001b[39m     \u001b[36mconst\u001b[39m loadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:356:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:16:00.100Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}}, {"workerIndex": 25, "parallelIndex": 0, "status": "failed", "duration": 3275, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:356:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}, "snippet": "\u001b[0m \u001b[90m 354 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 355 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 356 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 357 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 358 |\u001b[39m     \n \u001b[90m 359 |\u001b[39m     \u001b[36mconst\u001b[39m loadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 354 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 355 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 356 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 357 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 358 |\u001b[39m     \n \u001b[90m 359 |\u001b[39m     \u001b[36mconst\u001b[39m loadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:356:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:16:04.841Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库组件重构验证-性能和加载时间测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 356}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-3ffb2cac0313e736f426", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 353, "column": 7}]}, {"title": "知识库功能完整性测试", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 376, "column": 6, "specs": [{"title": "端到端用户流程测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 26, "parallelIndex": 0, "status": "failed", "duration": 3258, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:381:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}, "snippet": "\u001b[0m \u001b[90m 379 |\u001b[39m     \n \u001b[90m 380 |\u001b[39m     \u001b[90m// 1. 访问知识库首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 381 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 382 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 383 |\u001b[39m     \n \u001b[90m 384 |\u001b[39m     \u001b[90m// 2. 使用搜索功能\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 379 |\u001b[39m     \n \u001b[90m 380 |\u001b[39m     \u001b[90m// 1. 访问知识库首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 381 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 382 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 383 |\u001b[39m     \n \u001b[90m 384 |\u001b[39m     \u001b[90m// 2. 使用搜索功能\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:381:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T12:16:09.513Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}}, {"workerIndex": 27, "parallelIndex": 0, "status": "failed", "duration": 3387, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:381:16", "location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}, "snippet": "\u001b[0m \u001b[90m 379 |\u001b[39m     \n \u001b[90m 380 |\u001b[39m     \u001b[90m// 1. 访问知识库首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 381 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 382 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 383 |\u001b[39m     \n \u001b[90m 384 |\u001b[39m     \u001b[90m// 2. 使用搜索功能\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/knowledge\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/knowledge\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 379 |\u001b[39m     \n \u001b[90m 380 |\u001b[39m     \u001b[90m// 1. 访问知识库首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 381 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/knowledge'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 382 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 383 |\u001b[39m     \n \u001b[90m 384 |\u001b[39m     \u001b[90m// 2. 使用搜索功能\u001b[39m\u001b[0m\n\u001b[2m    at D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts:381:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-03T12:16:14.202Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "D:\\MysqlAi.De\\web-app\\test-results\\artifacts\\knowledge-knowledge-refactor-playwright-知识库功能完整性测试-端到端用户流程测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "D:\\MysqlAi.De\\web-app\\src\\tests\\knowledge\\knowledge-refactor-playwright.spec.ts", "column": 16, "line": 381}}], "status": "unexpected"}], "id": "e26cd16a1c4c33c08959-a0d6de3eaee6e62cf058", "file": "knowledge/knowledge-refactor-playwright.spec.ts", "line": 377, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-03T12:13:52.295Z", "duration": 145795.659, "expected": 3, "skipped": 0, "unexpected": 14, "flaky": 0}}