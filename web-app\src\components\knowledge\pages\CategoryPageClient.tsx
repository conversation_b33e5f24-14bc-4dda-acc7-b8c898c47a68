'use client';

// MySQLAi.de - CategoryPageClient分类页面客户端组件
// 处理分类页面的交互功能、筛选和排序

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Grid,
  List,
  BookOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { KnowledgeCategory, KnowledgeItem } from '@/lib/types';
import Breadcrumb from '@/components/ui/Breadcrumb';
import KnowledgeCard from '@/components/knowledge/KnowledgeCard';

// 这些配置已移至左侧导航，此处不再需要

interface CategoryPageClientProps {
  category: KnowledgeCategory;
  items: KnowledgeItem[];
  categorySlug: string;
}

export default function CategoryPageClient({
  category,
  items,
  categorySlug
}: CategoryPageClientProps) {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 简化的知识点列表（筛选和排序功能已移至左侧导航）
  const displayItems = items;

  // 处理知识点选择
  const handleItemSelect = (item: KnowledgeItem) => {
    // 跳转到知识点详情页面
    router.push(`/knowledge/${categorySlug}/${item.id}`);
  };

  return (
    <div className="flex flex-col min-h-full bg-white">
      {/* 面包屑导航 */}
      <div className="flex-shrink-0 border-b border-mysql-border bg-white">
        <div className="px-6 py-4">
          <Breadcrumb pathname={`/knowledge/${categorySlug}`} />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-6 py-8">

          {/* 顶部工具栏 - 只保留视图切换 */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-mysql-text mr-4">
                {category.name}
              </h1>
              <span className="text-sm text-mysql-text-light">
                共 {items.length} 个知识点
              </span>
            </div>

            {/* 视图切换 */}
            <div className="flex items-center bg-mysql-primary-light rounded-lg p-1">
              <button
                type="button"
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-md transition-colors duration-200',
                  viewMode === 'grid'
                    ? 'bg-mysql-primary text-white'
                    : 'text-mysql-text-light hover:text-mysql-primary'
                )}
                aria-label="网格视图"
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-md transition-colors duration-200',
                  viewMode === 'list'
                    ? 'bg-mysql-primary text-white'
                    : 'text-mysql-text-light hover:text-mysql-primary'
                )}
                aria-label="列表视图"
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* 知识点列表 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {displayItems.length > 0 ? (
              <div className={cn(
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              )}>
                {displayItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.4,
                      delay: 0.3 + index * 0.05,
                      ease: "easeOut"
                    }}
                  >
                    <KnowledgeCard
                      item={item}
                      displayMode={viewMode}
                      onClick={() => handleItemSelect(item)}
                    />
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <BookOpen className="w-16 h-16 text-mysql-text-light mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-mysql-text mb-2">
                  暂无知识点
                </h3>
                <p className="text-mysql-text-light">
                  该分类下暂时没有知识点内容
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
