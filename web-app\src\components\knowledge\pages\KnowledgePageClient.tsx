'use client';

// MySQLAi.de - KnowledgePageClient客户端组件
// 处理知识库首页的交互功能和状态管理

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Clock,
  Star,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useKnowledgeData } from '@/hooks/useKnowledgeData';
import { KnowledgeItem } from '@/lib/types';
import KnowledgeCard from '@/components/knowledge/KnowledgeCard';
import IntelligentSearch from '@/components/knowledge/IntelligentSearch';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { StateWrapper } from '@/components/ui/StateComponents';

export default function KnowledgePageClient() {
  const router = useRouter();
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // 使用 useKnowledgeData Hook 获取真实数据
  const {
    data: dbArticles,
    loading,
    error,
    retry
  } = useKnowledgeData('articles', {
    includeCodeExamples: true,
    includeRelated: true
  }, {
    autoFetch: true, // 启用自动获取
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    progressiveLoading: true, // 启用渐进加载
    prioritizeBasicData: true, // 优先加载基础数据
    debug: process.env.NODE_ENV === 'development'
  });

  // 数据处理 - useKnowledgeData 已经返回了正确格式的数据
  const frontendArticles = useMemo(() => {
    if (!dbArticles || dbArticles.length === 0) return [];
    return dbArticles as KnowledgeItem[];
  }, [dbArticles]);

  // 计算热门文章（基于相关项目数量）
  const popularItems = useMemo(() => {
    return frontendArticles
      .sort((a, b) => a.order_index - b.order_index)
      .slice(0, 6);
  }, [frontendArticles]);

  // 计算最近更新的文章
  const recentItems = useMemo(() => {
    return frontendArticles
      .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())
      .slice(0, 6);
  }, [frontendArticles]);

  // 页面加载状态管理
  useEffect(() => {
    if (!loading) {
      setIsLoaded(true);
    }
  }, [loading]);

  // 处理知识点选择
  const handleKnowledgeSelect = (item: KnowledgeItem) => {
    // 跳转到知识点详情页面
    router.push(`/knowledge/${item.category_id}/${item.id}`);
  };

  // 处理搜索结果选择
  const handleSearchResultSelect = (result: any) => {
    // 跳转到文章详情页面
    router.push(`/knowledge/${result.id}`);
  };

  // 模拟分类和标签数据（实际项目中应该从API获取）
  const searchCategories = [
    { value: '1', label: 'MySQL基础', count: 25 },
    { value: '2', label: '查询优化', count: 18 },
    { value: '3', label: '索引设计', count: 22 },
    { value: '4', label: '存储引擎', count: 15 },
    { value: '5', label: '备份恢复', count: 12 }
  ];

  const searchTags = [
    { value: 'select', label: 'SELECT查询', count: 45 },
    { value: 'index', label: '索引优化', count: 38 },
    { value: 'performance', label: '性能调优', count: 32 },
    { value: 'innodb', label: 'InnoDB', count: 28 },
    { value: 'backup', label: '数据备份', count: 20 },
    { value: 'replication', label: '主从复制', count: 15 },
    { value: 'transaction', label: '事务处理', count: 25 },
    { value: 'lock', label: '锁机制', count: 18 }
  ];

  return (
    <div className="flex flex-col min-h-full bg-white" data-testid="knowledge-page">
      {/* 面包屑导航 */}
      <div className="flex-shrink-0 border-b border-mysql-border bg-white">
        <div className="px-6 py-4">
          <Breadcrumb pathname="/knowledge" />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-6 py-8">
          <StateWrapper
            loading={loading}
            error={error || undefined}
            isEmpty={!loading && !error && popularItems.length === 0 && recentItems.length === 0}
            loadingProps={{
              message: '正在加载知识库数据...',
              variant: 'skeleton',
              itemCount: 6
            }}
            errorProps={{
              title: '加载失败',
              error: error || '无法加载知识库数据，请检查网络连接或稍后重试',
              onRetry: retry,
              variant: 'network'
            }}
            emptyProps={{
              title: '暂无知识库内容',
              message: '知识库正在建设中，敬请期待更多精彩内容',
              action: {
                label: '刷新页面',
                onClick: () => window.location.reload()
              }
            }}
          >
            {/* 智能搜索区域 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
              transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
              className="mb-12"
            >
              <div className="text-center mb-8">
                <h1 className="text-4xl font-bold text-mysql-text mb-4">
                  MySQL 知识库
                </h1>
                <p className="text-lg text-mysql-text-light max-w-2xl mx-auto">
                  探索全面的MySQL知识体系，从基础概念到高级优化技巧
                </p>
              </div>

              <IntelligentSearch
                placeholder="搜索MySQL知识点、教程、最佳实践..."
                autoFocus={false}
                showAdvancedFilters={true}
                showSearchHistory={true}
                maxResults={20}
                onResultSelect={handleSearchResultSelect}
                categories={searchCategories}
                availableTags={searchTags}
                className="max-w-4xl mx-auto"
              />
            </motion.div>

            {/* 热门推荐区域 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              className="mb-12"
              data-testid="popular-articles"
            >
              <div className="flex items-center mb-6">
                <Star className="w-6 h-6 text-mysql-primary mr-3" />
                <h2 className="text-2xl font-bold text-mysql-text">
                  热门推荐
                </h2>
              </div>
              <p className="text-mysql-text-light mb-8">
                最受欢迎的MySQL知识点，快速提升你的技能
              </p>

              {popularItems.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {popularItems.map((item, index) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1, ease: "easeOut" }}
                      data-testid="popular-article"
                    >
                      <KnowledgeCard
                        item={item}
                        displayMode="grid"
                        onClick={() => handleKnowledgeSelect(item)}
                      />
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-mysql-text-light">暂无热门推荐内容</p>
                </div>
              )}

                {/* 查看更多按钮 */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                  transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                  className="text-center mt-8"
                >
                  <button
                    type="button"
                    onClick={() => router.push('/knowledge/categories')}
                    className={cn(
                      'inline-flex items-center px-6 py-3 rounded-lg',
                      'bg-mysql-primary text-white border border-mysql-primary',
                      'hover:bg-mysql-primary-dark hover:border-mysql-primary-dark',
                      'transition-all duration-200 ease-out',
                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
                    )}
                  >
                    <span className="mr-2">查看所有知识点</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </motion.div>
              </motion.div>

              {/* 最近更新区域 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
                transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
                data-testid="recent-articles"
              >
                <div className="flex items-center mb-6">
                  <Clock className="w-6 h-6 text-mysql-primary mr-3" />
                  <h2 className="text-2xl font-bold text-mysql-text">
                    最近更新
                  </h2>
                </div>
                <p className="text-mysql-text-light mb-8">
                  最新添加和更新的MySQL知识内容
                </p>

                {recentItems.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {recentItems.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                        transition={{ duration: 0.4, delay: 0.7 + index * 0.1, ease: "easeOut" }}
                        data-testid="recent-article"
                      >
                        <KnowledgeCard
                          item={item}
                          displayMode="grid"
                          onClick={() => handleKnowledgeSelect(item)}
                        />
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-mysql-text-light">暂无最近更新内容</p>
                  </div>
                )}
              </motion.div>
          </StateWrapper>
        </div>
      </div>
    </div>
  );
}
