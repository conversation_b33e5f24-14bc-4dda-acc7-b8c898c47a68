'use client';

// MySQLAi.de - KnowledgeSidebarWrapper客户端组件包装器
// 用于解决hydration mismatch问题，确保KnowledgeSidebar只在客户端渲染

import React, { useState, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, Clock } from 'lucide-react';
import { useNavigation } from '@/contexts/NavigationContext';
import { getKnowledgeItemsByCategory } from '@/lib/knowledge';
import { useKnowledgeData } from '@/hooks/useKnowledgeData';
import { mapDatabaseCategoriesToFrontend, getIconEmoji } from '@/lib/utils';
import { hybridSearchHistory } from '@/lib/api/search-history';
import { SidebarSkeleton, ErrorState, LoadingState } from '@/components/ui/StateComponents';
import SearchHistory from './SearchHistory';


// 直接实现KnowledgeSidebar，包含完整的交互功能
function KnowledgeSidebar() {
  const router = useRouter();
  const navigation = useNavigation();

  // 状态管理
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSearchHistory, setShowSearchHistory] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const [categoryArticles, setCategoryArticles] = useState<Record<string, any[]>>({});

  // 使用 useKnowledgeData Hook 获取真实分类数据
  const {
    data: dbCategories,
    loading,
    error,
    retry
  } = useKnowledgeData('categories', {
    includeStats: true
  }, {
    autoFetch: true, // 启用自动获取
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    debug: process.env.NODE_ENV === 'development'
  });

  // 转换数据格式并排序 - 确保类型正确
  const categories = useMemo(() => {
    if (!dbCategories || dbCategories.length === 0) return [];
    return (dbCategories as any[])
      .sort((a: any, b: any) => a.order_index - b.order_index);
  }, [dbCategories]);



  // 处理分类点击
  const handleCategoryClick = async (categoryId: string) => {
    // 切换展开状态
    const newExpanded = new Set(expandedCategories);
    const wasExpanded = newExpanded.has(categoryId);

    if (wasExpanded) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);

      // 如果是展开操作且还没有加载过该分类的文章，则获取文章数据
      if (!categoryArticles[categoryId]) {
        try {
          const articles = await getKnowledgeItemsByCategory(categoryId);
          setCategoryArticles(prev => ({
            ...prev,
            [categoryId]: articles.map(article => ({
              id: article.id,
              title: article.title
            }))
          }));
        } catch (error) {
          console.error(`获取分类 ${categoryId} 的文章失败:`, error);
          // 即使获取文章失败，也要设置一个空数组，避免重复请求
          setCategoryArticles(prev => ({
            ...prev,
            [categoryId]: []
          }));
        }
      }
    }

    setExpandedCategories(newExpanded);

    // 更新导航状态
    navigation.setCurrentCategory(categoryId);

    // 跳转到分类页面
    router.push(`/knowledge/${categoryId}`);
  };

  // 处理知识点点击
  const handleItemClick = (categoryId: string, itemId: string) => {
    navigation.setCurrentItem(itemId);
    router.push(`/knowledge/${categoryId}/${itemId}`);
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    navigation.setSearchQuery(query);

    // 如果查询不为空，保存到搜索历史
    if (query.trim()) {
      // 这里可以添加结果计数，暂时设为0
      hybridSearchHistory.addRecord(query.trim(), 0);
    }
  };

  // 处理搜索历史选择
  const handleSearchHistorySelect = (query: string) => {
    navigation.setSearchQuery(query);
    setShowSearchHistory(false);
  };

  // 处理搜索框焦点
  const handleSearchFocus = () => {
    setSearchFocused(true);
    setShowSearchHistory(true);
  };

  // 处理搜索框失焦
  const handleSearchBlur = () => {
    setSearchFocused(false);
    // 延迟隐藏搜索历史，允许点击历史项
    setTimeout(() => setShowSearchHistory(false), 200);
  };

  // 获取分类下的知识点
  const getCategoryItems = (categoryId: string): { id: string; title: string }[] => {
    return categoryArticles[categoryId] || [];
  };

  // 获取分类的文章数量（从API数据中获取）
  const getCategoryArticleCount = (category: any): number => {
    // 优先使用API返回的article_count
    if (typeof category.article_count === 'number') {
      return category.article_count;
    }
    // 如果没有，使用本地缓存的文章数据
    return categoryArticles[category.id]?.length || 0;
  };
  return (
    <div className="flex flex-col h-full bg-white">
      {/* 头部区域 */}
      <div className="flex-shrink-0 p-4 border-b border-mysql-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-mysql-text">知识库导航</h2>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-mysql-text-light" />
            </div>
            <input
              type="text"
              placeholder="搜索知识点..."
              value={navigation.searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              onFocus={handleSearchFocus}
              onBlur={handleSearchBlur}
              className="w-full pl-10 pr-10 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary/30"
              data-testid="search-input"
            />
            {navigation.searchQuery && (
              <button
                type="button"
                onClick={() => handleSearch('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-mysql-text-light hover:text-mysql-text"
              >
                ×
              </button>
            )}
          </div>

          {/* 搜索历史下拉 */}
          {showSearchHistory && (
            <div className="absolute top-full left-0 right-0 mt-1 z-50">
              <SearchHistory
                onSelectQuery={handleSearchHistorySelect}
                onClose={() => setShowSearchHistory(false)}
                maxItems={5}
                className="w-full"
              />
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {/* 加载状态 */}
        {loading && <SidebarSkeleton itemCount={6} />}

        {/* 错误状态 */}
        {error && !loading && (
          <div className="p-4">
            <ErrorState
              error="分类数据加载失败"
              title="加载失败"
              onRetry={retry}
              retryLabel="重试"
              variant="network"
              className="py-8"
            />
          </div>
        )}

        {/* 正常内容 */}
        {!loading && !error && (
          <div className="p-4 space-y-2" data-testid="knowledge-categories">
            {categories.map((category) => {
              const isExpanded = expandedCategories.has(category.id);
              const isActive = navigation.currentCategory === category.id;
              const categoryItems = getCategoryItems(category.id);
              const articleCount = getCategoryArticleCount(category);

              return (
                <div key={category.id} className="space-y-1" data-testid="category-item">
                  {/* 分类标题 */}
                  <button
                    type="button"
                    onClick={() => handleCategoryClick(category.id)}
                    className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-all duration-200 hover:bg-mysql-primary-light hover:text-mysql-primary ${
                      isActive ? 'bg-mysql-primary text-white' : 'text-mysql-text'
                    }`}
                  >
                    <span className="w-4 h-4 mr-2 flex-shrink-0">{getIconEmoji(category.icon || 'FolderOpen')}</span>
                    <span className="flex-1 font-medium">{category.name}</span>
                    {articleCount > 0 && (
                      <span className="text-xs bg-mysql-primary-light text-mysql-primary px-2 py-1 rounded-full mr-2">
                        {articleCount}
                      </span>
                    )}
                    <span className="w-4 h-4 flex-shrink-0">
                      {isExpanded ? '▼' : '▶'}
                    </span>
                  </button>

                  {/* 分类下的知识点 */}
                  {isExpanded && (
                    <div className="ml-6 space-y-1">
                      {categoryItems.map((item) => (
                        <button
                          key={item.id}
                          type="button"
                          onClick={() => handleItemClick(category.id, item.id)}
                          className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors hover:bg-mysql-primary-light hover:text-mysql-primary ${
                            navigation.currentItem === item.id
                              ? 'bg-mysql-primary-light text-mysql-primary'
                              : 'text-mysql-text-light'
                          }`}
                        >
                          <span className="w-3 h-3 mr-2 flex-shrink-0">📄</span>
                          <span className="text-sm">{item.title}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 底部统计信息 */}
      <div className="flex-shrink-0 p-4 border-t border-mysql-border bg-mysql-bg-light">
        {!loading && !error && (
          <>
            <div className="text-xs text-mysql-text-light text-center">
              共 {categories.length} 个分类，{categories.reduce((total, cat) => total + getCategoryArticleCount(cat), 0)} 个知识点
            </div>
            <div className="text-xs text-mysql-text-light text-center mt-1">
              MySQL知识库 v1.0
            </div>
          </>
        )}
        {loading && (
          <LoadingState
            message="加载中..."
            size="sm"
            variant="spinner"
            className="py-2"
          />
        )}
      </div>
    </div>
  );
}

export default function KnowledgeSidebarWrapper() {
  return <KnowledgeSidebar />;
}
