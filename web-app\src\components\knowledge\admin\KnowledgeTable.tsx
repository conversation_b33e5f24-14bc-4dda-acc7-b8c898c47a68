'use client';

// MySQLAi.de - 知识库表格组件
// 管理后台专用的统一表格组件

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ChevronUp,
  ChevronDown,
  CheckSquare,
  Square
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

// 表格列配置类型
interface TableColumn<T = any> {
  key: string;
  title: string;
  width?: string;
  sortable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
}

// 表格操作类型
interface TableAction<T = any> {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: (record: T) => void;
  disabled?: (record: T) => boolean;
  color?: 'primary' | 'danger' | 'warning';
}

// 排序配置类型
interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

interface KnowledgeTableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  actions?: TableAction<T>[];
  loading?: boolean;
  selectable?: boolean;
  selectedKeys?: string[];
  onSelectionChange?: (keys: string[]) => void;
  sortConfig?: SortConfig;
  onSort?: (config: SortConfig) => void;
  rowKey?: string | ((record: T) => string);
  emptyText?: string;
  className?: string;
}

export default function KnowledgeTable<T = any>({
  columns,
  data,
  actions = [],
  loading = false,
  selectable = false,
  selectedKeys = [],
  onSelectionChange,
  sortConfig,
  onSort,
  rowKey = 'id',
  emptyText = '暂无数据',
  className
}: KnowledgeTableProps<T>) {

  // 获取行的唯一键
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return (record as any)[rowKey] || index.toString();
  };

  // 处理排序
  const handleSort = (key: string) => {
    if (!onSort) return;

    const direction = sortConfig?.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    onSort({ key, direction });
  };

  // 处理全选
  const handleSelectAll = () => {
    if (!onSelectionChange) return;

    const allKeys = data.map((record, index) => getRowKey(record, index));
    const isAllSelected = allKeys.every(key => selectedKeys.includes(key));
    
    if (isAllSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(allKeys);
    }
  };

  // 处理单行选择
  const handleSelectRow = (key: string) => {
    if (!onSelectionChange) return;

    const newSelectedKeys = selectedKeys.includes(key)
      ? selectedKeys.filter(k => k !== key)
      : [...selectedKeys, key];
    
    onSelectionChange(newSelectedKeys);
  };



  // 渲染表头
  const renderHeader = () => (
    <thead className="bg-gray-50">
      <tr>
        {/* 选择列 */}
        {selectable && (
          <th className="w-12 px-4 py-3 text-left">
            <button
              onClick={handleSelectAll}
              className="flex items-center justify-center w-5 h-5 text-mysql-primary hover:bg-mysql-primary-light rounded transition-colors duration-200"
            >
              {selectedKeys.length === data.length && data.length > 0 ? (
                <CheckSquare className="w-4 h-4" />
              ) : (
                <Square className="w-4 h-4" />
              )}
            </button>
          </th>
        )}

        {/* 数据列 */}
        {columns.map((column) => (
          <th
            key={column.key}
            className={cn(
              'px-4 py-3 text-sm font-semibold text-mysql-text',
              column.align === 'center' && 'text-center',
              column.align === 'right' && 'text-right',
              column.sortable && 'cursor-pointer hover:bg-gray-100 transition-colors duration-200'
            )}
            style={{ width: column.width }}
            onClick={() => column.sortable && handleSort(column.key)}
          >
            <div className="flex items-center space-x-1">
              <span>{column.title}</span>
              {column.sortable && (
                <div className="flex flex-col">
                  <ChevronUp className={cn(
                    'w-3 h-3 -mb-1',
                    sortConfig?.key === column.key && sortConfig.direction === 'asc'
                      ? 'text-mysql-primary'
                      : 'text-gray-400'
                  )} />
                  <ChevronDown className={cn(
                    'w-3 h-3',
                    sortConfig?.key === column.key && sortConfig.direction === 'desc'
                      ? 'text-mysql-primary'
                      : 'text-gray-400'
                  )} />
                </div>
              )}
            </div>
          </th>
        ))}

        {/* 操作列 */}
        {actions.length > 0 && (
          <th className="w-32 px-4 py-3 text-center text-sm font-semibold text-mysql-text">
            操作
          </th>
        )}
      </tr>
    </thead>
  );

  // 渲染表格行
  const renderRow = (record: T, index: number) => {
    const key = getRowKey(record, index);
    const isSelected = selectedKeys.includes(key);

    return (
      <tr
        key={key}
        className={cn(
          'border-b border-mysql-border hover:bg-gray-50 transition-colors duration-200',
          isSelected && 'bg-mysql-primary-light'
        )}
      >
        {/* 选择列 */}
        {selectable && (
          <td className="px-4 py-3">
            <button
              onClick={() => handleSelectRow(key)}
              className="flex items-center justify-center w-5 h-5 text-mysql-primary hover:bg-mysql-primary-light rounded transition-colors duration-200"
            >
              {isSelected ? (
                <CheckSquare className="w-4 h-4" />
              ) : (
                <Square className="w-4 h-4" />
              )}
            </button>
          </td>
        )}

        {/* 数据列 */}
        {columns.map((column) => (
          <td
            key={column.key}
            className={cn(
              'px-4 py-3 text-sm text-mysql-text',
              column.align === 'center' && 'text-center',
              column.align === 'right' && 'text-right'
            )}
          >
            {column.render 
              ? column.render((record as any)[column.key], record, index)
              : (record as any)[column.key]
            }
          </td>
        ))}

        {/* 操作列 - 直接显示按钮 */}
        {actions.length > 0 && (
          <td className="px-4 py-3 text-center">
            <div className="flex items-center justify-center space-x-1">
              {actions.map((action) => {
                const IconComponent = action.icon;
                const isDisabled = action.disabled?.(record) || false;

                return (
                  <Button
                    key={action.key}
                    variant="ghost"
                    size="sm"
                    onClick={() => !isDisabled && action.onClick(record)}
                    disabled={isDisabled}
                    icon={<IconComponent className="w-4 h-4" />}
                    className={cn(
                      'p-1.5 transition-colors duration-200',
                      isDisabled && 'opacity-50 cursor-not-allowed',
                      action.color === 'danger' && 'text-red-600 hover:bg-red-50 hover:text-red-700',
                      action.color === 'warning' && 'text-yellow-600 hover:bg-yellow-50 hover:text-yellow-700',
                      action.color === 'primary' && 'text-mysql-primary hover:bg-mysql-primary-light hover:text-mysql-primary-dark'
                    )}
                  />
                );
              })}
            </div>
          </td>
        )}
      </tr>
    );
  };

  return (
    <div className={cn('bg-white rounded-lg shadow-md border border-mysql-border overflow-hidden', className)}>
      <div className="overflow-x-auto">
        <table className="w-full">
          {renderHeader()}
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)} className="px-4 py-8 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-mysql-primary border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-mysql-text-light">加载中...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)} className="px-4 py-8 text-center text-mysql-text-light">
                  {emptyText}
                </td>
              </tr>
            ) : (
              data.map((record, index) => renderRow(record, index))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}


